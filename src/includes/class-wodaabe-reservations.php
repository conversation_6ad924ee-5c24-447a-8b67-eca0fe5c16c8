<?php
/**
 * Wodaabe Reservations System.
 *
 * Provides a couple of functionalities that allows reservations on wodaabe site.
 *
 * @class       Wodaabe_Reservations
 * @version     1.0.0
 * @package     Wodaabe_Reservations
 */

if (!defined('ABSPATH')) {
  exit;
}


require_once WODAABE_RESERVATIONS_PLUGIN_PATH . '/vendor/autoload.php';
use Mailgun\Mailgun;

/**
 * Wodaabe_Reservations class.
 */
class Wodaabe_Reservations {

  /**
   * Contructor
   */
  public function __construct() {
    \add_action('wp_mail_failed', 'onMailError', 10, 1);

    \add_action('admin_menu', array($this, 'register_admin_menus'));
    \add_action('wp_ajax_nopriv_wrs_process_ajax', array($this, 'wrs_process_ajax'));
    \add_action('wp_ajax_wrs_process_ajax', array($this, 'wrs_process_ajax'));

    \add_action('rest_api_init', array($this, 'create_stripe_webhook'));
    \add_action('rest_api_init', array($this, 'create_ical_export_endpoint'));
    \add_action('rest_api_init', array($this, 'create_email_reminders_endpoint'));
    \add_action('rest_api_init', array($this, 'create_ical_sync_endpoint'));
    \add_action('rest_api_init', array($this, 'create_hostaway_webhook'));
    //\add_action('rest_api_init', array($this, 'create_price_sync_endpoint'));
  }



  public function wrs_process_ajax() {
    wrs_log("Processing new AJAX Request with payload " . json_encode($_POST), 'INFO');
    $action = $_POST['wrs_action'];
    if (!empty($action)) {
      if (method_exists($this, $action)) {
        return $this->{$action}();
      } else {
          wrs_log("Method " . $action . " not found in " . get_class($this));
      }
    }
    wp_send_json_error($this->error("Une erreur est survenue! Merci de reéssayer plus tard."));
  }

  public function error($msg = 'An error occured!') {
    return [
      "status" => "failed",
      "message" => $msg,
      "data" => null,
    ];
  }

  public function success($data) {
    return [
      "status" => "success",
      "message" => "ok",
      "data" => $data,
    ];
  }

  public function get_by_id($table_name, $id = 0, $output = 'ARRAY_A') {
    global $wpdb;
    $table_name = $wpdb->prefix . $table_name;
    return $wpdb->get_row("SELECT * FROM {$table_name} WHERE id = " . "'$id'", $output);
  }

  public function get_items($sql, $output = 'ARRAY_A') {
    global $wpdb;
    return $wpdb->get_results($sql, $output);
  }

  /**
   * Get a configuration value from the configuration table
   *
   * @param string $name The name of the configuration
   * @return mixed The configuration value or null if not found
   */
  public function get_configuration($name) {
    global $wpdb;
    $table_name = $wpdb->prefix . WODAABE_RESERVATIONS_CONFIGURATION_TABLE;
    $result = $wpdb->get_row(
      $wpdb->prepare("SELECT * FROM {$table_name} WHERE name = %s", $name),
      ARRAY_A
    );

    if ($result) {
      return maybe_unserialize($result['value']);
    }

    return null;
  }

  /**
   * Save a configuration value to the configuration table
   *
   * @param string $name The name of the configuration
   * @param mixed $value The value to save
   * @return bool True on success, false on failure
   */
  public function save_configuration($name, $value) {
    global $wpdb;
    $table_name = $wpdb->prefix . WODAABE_RESERVATIONS_CONFIGURATION_TABLE;

    // Check if configuration already exists
    $existing = $wpdb->get_row(
      $wpdb->prepare("SELECT * FROM {$table_name} WHERE name = %s", $name),
      ARRAY_A
    );

    $serialized_value = maybe_serialize($value);

    if ($existing) {
      // Update existing configuration
      return $wpdb->update(
        $table_name,
        array(
          'value' => $serialized_value,
          'date_upd' => current_time('mysql')
        ),
        array('name' => $name),
        array('%s', '%s'),
        array('%s')
      );
    } else {
      // Insert new configuration
      return $wpdb->insert(
        $table_name,
        array(
          'name' => $name,
          'value' => $serialized_value,
          'date_add' => current_time('mysql'),
          'date_upd' => current_time('mysql')
        ),
        array('%s', '%s', '%s', '%s')
      );
    }
  }

  public function get_reservation_payment($payment_id = 0) {
    return $this->get_by_id(WODAABE_RESERVATIONS_PAYMENT_TABLE, $payment_id);
  }

  public function get_reservation_currency($currency_id = 0) {
    return $this->get_by_id(WODAABE_RESERVATIONS_CURRENCY_TABLE, $currency_id);
  }

  public function get_reservation_language($language_id = 0) {
    return $this->get_by_id(WODAABE_RESERVATIONS_LANGUAGE_TABLE, $language_id);
  }

  public function get_reservation_location($location_id = 0) {
    return $this->get_by_id(WODAABE_RESERVATIONS_LOCATION_TABLE, $location_id);
  }

  public function get_reservation_person($person_id = 0) {
    return $this->get_by_id(WODAABE_RESERVATIONS_PERSON_TABLE, $person_id);
  }

  public function get_reservation_items($reservation_id = 0) {
    global $wpdb;
    $table_name = $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE;
    $sql = "SELECT * FROM {$table_name} WHERE reservation_id = " . "'$reservation_id'";
    $items = $this->get_items($sql);
    foreach ($items as &$value) {
      $value["product"] = $this->get_by_id(WODAABE_RESERVATIONS_PRODUCT_TABLE, $value["product_id"]);
    }
    return $items;

  }

  public function get_reservation_full($id = 0) {
    $result = $this->get_by_id(WODAABE_RESERVATIONS_TABLE, $id);

    if ($result) {
      $result["payment"] = $this->get_reservation_payment($result["payment_id"]);
      $result["currency"] = $this->get_reservation_currency($result["currency_id"]);
      $result["language"] = $this->get_reservation_language($result["language_id"]);
      $result["person"] = $this->get_reservation_person($result["person_id"]);
      $result["items"] = $this->get_reservation_items($id);

      return $result;
    } else {
      return false;
    }
  }

  public function get_reservation($id = 0) {
    global $wpdb;

    $reservation_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
    $result = $wpdb->get_row("SELECT * FROM {$reservation_table_name} WHERE id = " . "'$id'");

    if ($result) {
      return $result;
    } else {
      return false;
    }
  }

  public function get_all_reservations() {
	global $wpdb;

	$table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
    $sql = "SELECT * FROM {$table_name}";
    $reservations = $this->get_items($sql);

	foreach ($reservations as &$reservation) {
		$reservation = $this->get_reservation_full($reservation["id"]);
	}

	return $reservations;
  }

  public function delete_reservation() {
    global $wpdb;

    $reservation = $this->get_reservation_full($_POST['id']);

    // Check if the reservation has a product with a listingMapId
    if ($reservation && isset($reservation['items']) && !empty($reservation['items'])) {
      $product_id = $reservation['items'][0]['product_id'];
      $product = $this->get_by_id(WODAABE_RESERVATIONS_PRODUCT_TABLE, $product_id);

      // If the product has a listingMapId, delete the reservation in Hostaway
      if (!empty($product['listingMapId'])) {
        wrs_log("Product has Hostaway listing ID: " . $product['listingMapId'] . ". Checking for Hostaway reservation...");

        // Find the Hostaway reservation ID
        $hostaway_id = $this->get_hostaway_reservation_id($reservation['date_arrive'], $reservation['date_departure']);

        if ($hostaway_id) {
          wrs_log("Found Hostaway reservation ID: " . $hostaway_id . ". Deleting from Hostaway...");
          $result = $this->delete_hostaway_reservation($hostaway_id);

          if ($result && $result['success']) {
            wrs_log("Successfully deleted reservation from Hostaway");
          } else {
            wrs_log("Failed to delete reservation from Hostaway: " . ($result['error'] ?? 'Unknown error'));
            // Continue with local deletion even if Hostaway deletion fails
          }
        } else {
          wrs_log("No matching Hostaway reservation found for dates: " . $reservation['date_arrive'] . " to " . $reservation['date_departure']);
        }
      }
    }

    // Delete the local reservation
    $wpdb->delete($wpdb->prefix . WODAABE_RESERVATIONS_TABLE, array('id' => $_POST['id']), array('%d'));

    $this->update_product_reservation_calendars();

    wp_send_json_success($this->success($reservation));
  }

  /*
    Hostaway API implementation
  */

  // Store Hostaway token
  public function store_hostaway_token($response) {
    wrs_log("Storing hostaway token");
    $token_data = json_decode($response, true);
    $token_data['expires_at'] = time() + $token_data['expires_in'];

    // Store token in the configuration table only
    $this->save_configuration('hostaway_token_data', $token_data);
  }

  // Get Hostaway token
  public function get_hostaway_token() {
    wrs_log("Retrieving hostaway token...");

    // Get token from configuration table
    $token_data = $this->get_configuration('hostaway_token_data');

    // Refresh if token expires in less than 24 hours
    if (!$token_data || time() > ($token_data['expires_at'] - 86400)) {
      wrs_log("Token not found or expired. Retrieving new hostaway token...");
      $post_fields = "grant_type=client_credentials&client_id=" . $_ENV["HOSTAWAY_CLIENT_ID"] . "&client_secret=" . $_ENV["HOSTAWAY_CLIENT_SECRET"] . "&scope=general";
      wrs_log("Post_fields: " . $post_fields);

      $curl = curl_init();

      curl_setopt_array($curl, array(
        CURLOPT_URL => "https://api.hostaway.com/v1/accessTokens",
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => $post_fields,
        CURLOPT_HTTPHEADER => array(
          "Cache-control: no-cache",
          "Content-type: application/x-www-form-urlencoded"
        ),
      ));

      $response = curl_exec($curl);
      $err = curl_error($curl);

      curl_close($curl);

      if ($err) {
        wrs_log("cURL Error #:" . $err);
      } else if ($response) {
        $this->store_hostaway_token($response);
        $token_data = $this->get_configuration('hostaway_token_data');
      }
    }

    return $token_data['access_token'] ?? null;
  }

    // Get the reservation id from hostaway for a given reservation
    public function get_hostaway_reservation_id($check_in, $check_out, $listing_map_id = null) {
        return $this->find_hostaway_reservation($check_in, $check_out, $listing_map_id);
    }

    /**
     * Private implementation of finding a Hostaway reservation
     * This is used internally by get_hostaway_reservation_id
     */
    private function find_hostaway_reservation($check_in, $check_out, $listing_map_id = null) {
        $token = $this->get_hostaway_token();
        if (empty($token)) {
            wrs_log("ERROR: Cannot find reservation - Authentication token is null");
            return null;
        }

        // Add a buffer day before and after to ensure we catch the reservation
        $start_date = date('Y-m-d', strtotime($check_in . ' -1 day'));
        $end_date = date('Y-m-d', strtotime($check_out . ' +1 day'));

        wrs_log("start_date: " . $start_date);
        wrs_log("end_date: " . $end_date);
        wrs_log("check_in: " . $check_in);
        wrs_log("check_out: " . $check_out);
        if ($listing_map_id) {
            wrs_log("listing_map_id: " . $listing_map_id);
        }

        // Build the query parameters
        $query_params = [
            'arrivalStartDate' => $start_date,
            'arrivalEndDate' => $end_date,
            'limit' => 20
        ];

        // Add listing ID if provided
        if ($listing_map_id) {
            $query_params['listingMapId'] = $listing_map_id;
        }

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.hostaway.com/v1/reservations?" . http_build_query($query_params),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => array(
                "Authorization: Bearer " . $token,
                "Cache-control: no-cache"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            wrs_log("ERROR: CURL error while finding reservation: " . $err);
            return null;
        }

        $data = json_decode($response, true);
        wrs_log("Hostaway API response: " . print_r($data, true));
        if (!isset($data['result'])) {
            wrs_log("ERROR: Invalid response from Hostaway API");
            return null;
        }

        // Find exact match
        foreach ($data['result'] as $reservation) {
            wrs_log("Checking Hostaway reservation ID: " . $reservation['id']);

            // If listing_map_id is provided, make sure it matches
            if ($listing_map_id && $reservation['listingMapId'] != $listing_map_id) {
                wrs_log("Skipping reservation - listing ID mismatch: " . $reservation['listingMapId'] . " vs " . $listing_map_id);
                continue;
            }

            if ($reservation['arrivalDate'] === $check_in && $reservation['departureDate'] === $check_out) {
                wrs_log("Found matching Hostaway reservation ID: " . $reservation['id']);
                return $reservation['id'];
            }
        }

        wrs_log("No matching Hostaway reservation found for dates: " . $check_in . " to " . $check_out);
        return null;
    }

  // Class property to store the departure date for use in the availability check
  private $departure_date;

  // Check if dates are available in Hostaway for a specific listing
  public function check_hostaway_availability($listing_map_id, $arrival_date, $departure_date) {
    wrs_log("Checking Hostaway availability for listing #" . $listing_map_id . " from " . $arrival_date . " to " . $departure_date);

    // Store the departure date as a class property for use in the function
    $this->departure_date = $departure_date;

    // Get token and validate
    $token = $this->get_hostaway_token();
    if (empty($token)) {
      wrs_log("ERROR: Cannot check availability - Authentication token is null");
      return array(
        'available' => false,
        'error' => 'Authentication failed - no valid token'
      );
    }

    try {
      $curl = curl_init();
      if (!$curl) {
        throw new Exception("Failed to initialize CURL");
      }

      // Format the URL with query parameters - using the calendar endpoint
      $url = "https://api.hostaway.com/v1/listings/" . $listing_map_id . "/calendar?" . http_build_query([
        'startDate' => $arrival_date,
        'endDate' => $departure_date
      ]);
      wrs_log("Hostaway calendar URL: " . $url);

      curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "GET",
        CURLOPT_HTTPHEADER => array(
          "Authorization: Bearer " . $token,
          "Cache-control: no-cache"
        ),
      ));

      $response = curl_exec($curl);
      $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
      $err = curl_error($curl);

      curl_close($curl);

      if ($err) {
        wrs_log("ERROR: CURL error while checking availability: " . $err);
        return array(
          'available' => false,
          'error' => 'API connection error: ' . $err
        );
      }

      $data = json_decode($response, true);
      wrs_log("Hostaway calendar response: " . json_encode($data));

      // Check if the API returned an error
      if (isset($data['status']) && $data['status'] === 'fail') {
        wrs_log("ERROR: API returned error: " . ($data['message'] ?? 'Unknown error'));
        return array(
          'available' => false,
          'error' => 'API Error: ' . ($data['message'] ?? 'Unknown error')
        );
      }

      // Check if the result contains calendar data
      if (!isset($data['result']) || !is_array($data['result'])) {
        wrs_log("ERROR: Invalid response format from Hostaway API");
        return array(
          'available' => false,
          'error' => 'Invalid response from Hostaway API'
        );
      }

      // Check if all dates in the range are available
      $all_available = true;
      $unavailable_dates = [];
      $departure_date_available = true;

      // Log the first date entry to understand the structure
      if (!empty($data['result']) && isset($data['result'][0])) {
        wrs_log("First date entry structure: " . json_encode($data['result'][0]));
      }

      // Special handling for the departure date - it should be available for booking
      // Find the departure date in the results
      $departure_date_info = null;
      foreach ($data['result'] as $date_info) {
        if (isset($date_info['date']) && $date_info['date'] === $this->departure_date) {
          $departure_date_info = $date_info;
          break;
        }
      }

      // For the departure date, we need to check if it's marked as unavailable
      // but only because of a reservation that ends on that day
      if ($departure_date_info) {
        wrs_log("Found departure date info: " . json_encode($departure_date_info));

        // If the departure date is marked as unavailable (isAvailable = 0)
        if (isset($departure_date_info['isAvailable']) && $departure_date_info['isAvailable'] == 0) {
          // Check if there are reservations for this date
          if (isset($departure_date_info['reservations']) && !empty($departure_date_info['reservations'])) {
            $all_are_checkouts = true;

            // Check each reservation on this date
            foreach ($departure_date_info['reservations'] as $reservation) {
              // If any reservation has this date as its departure date (checkout day)
              if (isset($reservation['departureDate']) && $reservation['departureDate'] === $this->departure_date) {
                // This is a checkout day for an existing reservation, so it's actually available for a new booking
                wrs_log("Departure date " . $this->departure_date . " has a reservation ending on this day");
              } else {
                // This reservation doesn't end on this day, so the day is truly unavailable
                $all_are_checkouts = false;
                wrs_log("Departure date " . $this->departure_date . " has a reservation that doesn't end on this day");
              }
            }

            // If all reservations on this day are checkouts, mark the day as available
            if ($all_are_checkouts) {
              wrs_log("All reservations on departure date " . $this->departure_date . " are checkouts, marking as available");
              $departure_date_available = true;
            } else {
              wrs_log("Departure date " . $this->departure_date . " has reservations that aren't checkouts, marking as unavailable");
              $departure_date_available = false;
            }
          }
        } else {
          // The departure date is already marked as available
          wrs_log("Departure date " . $this->departure_date . " is already marked as available");
          $departure_date_available = true;
        }
      }

      foreach ($data['result'] as $date_info) {
        // Skip checking the departure date - we'll handle it separately
        if (isset($date_info['date']) && $date_info['date'] === $this->departure_date) {
          continue;
        }

        // Check if the date is available using isAvailable field (0 = not available, 1 = available)
        // Also log the status field which provides more details about why a date is unavailable
        if (isset($date_info['isAvailable']) && $date_info['isAvailable'] == 0) {
          $all_available = false;
          if (isset($date_info['date'])) {
            $unavailable_dates[] = $date_info['date'];
          }

          // Log detailed information about unavailable dates
          $status_info = isset($date_info['status']) ? $date_info['status'] : 'unknown';
          $reservation_info = '';

          // If there are reservations for this date, log them
          if (isset($date_info['reservations']) && !empty($date_info['reservations'])) {
            $reservation_info = " - Has reservation: ID=" . $date_info['reservations'][0]['id'];

            // If the reservation has arrival and departure dates, log them
            if (isset($date_info['reservations'][0]['arrivalDate']) && isset($date_info['reservations'][0]['departureDate'])) {
              $reservation_info .= ", Arrival=" . $date_info['reservations'][0]['arrivalDate'] .
                                  ", Departure=" . $date_info['reservations'][0]['departureDate'];
            }
          }

          wrs_log("Date " . $date_info['date'] . " is not available. Status: " . $status_info . $reservation_info);
        } else {
          wrs_log("Date " . $date_info['date'] . " is available.");
        }
      }

      // Combine the results - all dates must be available
      $all_available = $all_available && $departure_date_available;

      if (!$all_available) {
        wrs_log("Dates not available in Hostaway: " . implode(', ', $unavailable_dates));
        return array(
          'available' => false,
          'error' => 'Selected dates are not available in Hostaway',
          'unavailable_dates' => $unavailable_dates
        );
      }

      wrs_log("Dates are available in Hostaway");
      return array(
        'available' => true
      );

    } catch (\Exception $e) {
      wrs_log("ERROR: Exception while checking Hostaway availability: " . $e->getMessage());
      return array(
        'available' => false,
        'error' => 'Error checking availability: ' . $e->getMessage()
      );
    }
  }

  // Creates a reservation in Hostaway
  public function create_hostaway_reservation($reservation_id) {
    wrs_log("Creating Hostaway reservation for local reservation #" . $reservation_id);

    // Get the local reservation details
    $reservation = $this->get_reservation_full($reservation_id);
    if (!$reservation) {
      wrs_log("ERROR: Cannot find local reservation #" . $reservation_id);
      return array(
        'success' => false,
        'error' => 'Local reservation not found'
      );
    }

    // Get token and validate
    $token = $this->get_hostaway_token();
    if (empty($token)) {
      wrs_log("ERROR: Cannot create reservation - Authentication token is null");
      return array(
        'success' => false,
        'error' => 'Authentication failed - no valid token'
      );
    }

    // Get the product (listing) details
    $product_id = $reservation['items'][0]['product_id'];
    $product = $this->get_by_id(WODAABE_RESERVATIONS_PRODUCT_TABLE, $product_id);

    // Check if the product has a listingMapId
    if (empty($product['listingMapId'])) {
      wrs_log("ERROR: Cannot create reservation in Hostaway - Product #" . $product_id . " does not have a listingMapId");
      return array(
        'success' => false,
        'error' => 'Product does not have a Hostaway listing ID (listingMapId)'
      );
    }

    // Get the person details
    $person = $reservation['person'];

    // Get payment information
    $payment = null;
    if (isset($reservation['payment_id']) && !empty($reservation['payment_id'])) {
      global $wpdb;
      $payment_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE;
      $payment = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM {$payment_table_name} WHERE id = %d", $reservation['payment_id']),
        ARRAY_A
      );
    }

    // Prepare the reservation data for Hostaway with all available information
    $reservation_data = array(
      // Required fields
      'listingMapId' => $product['listingMapId'],
      'channelId' => 2017, // Direct/Website channel ID in Hostaway
      'arrivalDate' => $reservation['date_arrive'],
      'departureDate' => $reservation['date_departure'],
      'status' => 'new', // Hostaway reservation status
      'externalReservationId' => $reservation['uuid'], // Use our UUID as external ID

      // Guest information
      'guestName' => $person['first_name'] . ' ' . $person['last_name'],
      'guestFirstName' => $person['first_name'] ?? '',
      'guestLastName' => $person['last_name'] ?? '',
      'guestEmail' => $person['email'] ?? '',
      'guestPhone' => $person['phone'] ?? '',
      'guestCountry' => $person['country'] ?? '',
      'guestZipCode' => '', // Not stored in our system
      'guestAddress' => '', // Not stored in our system
      'guestCity' => '', // Not stored in our system

      // Guest counts
      'numberOfGuests' => intval($reservation['adult_count']) + intval($reservation['child_count']),
      'adults' => intval($reservation['adult_count']),
      'children' => intval($reservation['child_count']),
      'infants' => 0, // Not stored in our system
      'pets' => 0, // Not stored in our system

      // Additional information
      'guestNote' => $person['comment'] ?? '',
      'hostNote' => '', // Not stored in our system

      // Payment information
      'totalPrice' => $payment && isset($payment['amount']) ? floatval($payment['amount']) : 0,
      'currency' => 'EUR', // Default currency
      'isPaid' => $payment && $payment['status'] === 'COMPLETED' ? 1 : 0,
    );

    // If this is a booking made for someone else (is_guess = 1)
    if (isset($person['is_guess']) && $person['is_guess'] == 1) {
      // Add buyer information as additional notes
      $reservation_data['hostNote'] = "Booking made by: " .
        $person['buyer_first_name'] . ' ' . $person['buyer_last_name'] .
        " (Email: " . $person['buyer_email'] . ", Phone: " . $person['buyer_phone'] . ")";
    }

    wrs_log("Hostaway reservation data: " . json_encode($reservation_data));

    try {
      $curl = curl_init();
      if (!$curl) {
        throw new Exception("Failed to initialize CURL");
      }

      curl_setopt_array($curl, array(
        CURLOPT_URL => "https://api.hostaway.com/v1/reservations",
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => json_encode($reservation_data),
        CURLOPT_HTTPHEADER => array(
          "Authorization: Bearer " . $token,
          "Cache-control: no-cache",
          "Content-type: application/json"
        ),
      ));

      $response = curl_exec($curl);
      $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
      $err = curl_error($curl);

      curl_close($curl);

      if ($err) {
        wrs_log("CURL Error while creating reservation: " . $err);
        return array(
          'success' => false,
          'error' => $err
        );
      }

      // Check HTTP response code
      if ($http_code >= 400) {
        wrs_log("API Error while creating reservation: HTTP " . $http_code . " - " . $response);
        return array(
          'success' => false,
          'error' => 'API Error: ' . $http_code,
          'response' => $response
        );
      }

      $response_data = json_decode($response, true);

      if (isset($response_data['result']['id'])) {
        $hostaway_id = $response_data['result']['id'];
        wrs_log("Successfully created Hostaway reservation #" . $hostaway_id);

        // Store the Hostaway reservation ID in our database
        $this->save_configuration('hostaway_reservation_' . $reservation_id, $hostaway_id);

        return array(
          'success' => true,
          'hostaway_id' => $hostaway_id,
          'response' => $response_data
        );
      } else {
        wrs_log("Failed to extract Hostaway reservation ID from response");
        return array(
          'success' => false,
          'error' => 'Invalid response format',
          'response' => $response_data
        );
      }

    } catch (Exception $e) {
      wrs_log("Exception while creating reservation: " . $e->getMessage());
      return array(
        'success' => false,
        'error' => $e->getMessage()
      );
    }
  }

  // Cancels a reservation in Hostaway
  public function cancel_hostaway_reservation($id) {
    // Log the cancellation attempt
    wrs_log("Attempting to cancel Hostaway reservation #" . $id);

    // Get token and validate
    $token = $this->get_hostaway_token();
    if (empty($token)) {
        wrs_log("ERROR: Cannot cancel reservation - Authentication token is null");
        return array(
            'success' => false,
            'error' => 'Authentication failed - no valid token'
        );
    }

    // Validate reservation ID
    if (empty($id) || !is_numeric($id)) {
        wrs_log("ERROR: Invalid reservation ID provided");
        return array(
            'success' => false,
            'error' => 'Invalid reservation ID'
        );
    }

    try {
        $curl = curl_init();
        if (!$curl) {
            throw new Exception("Failed to initialize CURL");
        }

        // Prepare the request body with cancelledBy parameter
        $request_body = json_encode(array(
            'cancelledBy' => 'host'
        ));

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.hostaway.com/v1/reservations/" . $id . "/statuses/cancelled",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "PUT",
            CURLOPT_POSTFIELDS => $request_body,
            CURLOPT_HTTPHEADER => array(
                "Authorization: Bearer " . $token,
                "Cache-control: no-cache",
                "Content-type: application/json"
            ),
        ));

        $response = curl_exec($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            wrs_log("CURL Error while cancelling reservation #" . $id . ": " . $err);
            return array(
                'success' => false,
                'error' => $err
            );
        }

        // Check HTTP response code
        if ($http_code >= 400) {
            wrs_log("API Error while cancelling reservation #" . $id . ": HTTP " . $http_code . " - " . $response);
            return array(
                'success' => false,
                'error' => 'API Error: ' . $http_code,
                'response' => $response
            );
        }

        wrs_log("Successfully cancelled Hostaway reservation #" . $id);
        return array(
            'success' => true,
            'response' => $response
        );

    } catch (Exception $e) {
        wrs_log("Exception while cancelling reservation #" . $id . ": " . $e->getMessage());
        return array(
            'success' => false,
            'error' => $e->getMessage()
        );
    }
  }

  // Deletes a reservation in hostaway records
  public function delete_hostaway_reservation($id) {
    // Log the deletion attempt
    wrs_log("Attempting to delete hostaway reservation #" . $id);

    // Get token and validate
    $token = $this->get_hostaway_token();
    if (empty($token)) {
        wrs_log("ERROR: Cannot delete reservation - Authentication token is null");
        return array(
            'success' => false,
            'error' => 'Authentication failed - no valid token'
        );
    }

    // Validate reservation ID
    if (empty($id) || !is_numeric($id)) {
        wrs_log("ERROR: Invalid reservation ID provided");
        return array(
            'success' => false,
            'error' => 'Invalid reservation ID'
        );
    }

    try {
        $curl = curl_init();
        if (!$curl) {
            throw new Exception("Failed to initialize CURL");
        }

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.hostaway.com/v1/reservations/" . $id,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "DELETE",
            CURLOPT_HTTPHEADER => array(
                "Authorization: Bearer " . $token,
                "Cache-control: no-cache",
                "Content-type: application/json"
            ),
        ));

        $response = curl_exec($curl);
        $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            wrs_log("CURL Error while deleting reservation #" . $id . ": " . $err);
            return array(
                'success' => false,
                'error' => $err
            );
        }

        // Check HTTP response code
        if ($http_code >= 400) {
            wrs_log("API Error while deleting reservation #" . $id . ": HTTP " . $http_code . " - " . $response);
            return array(
                'success' => false,
                'error' => 'API Error: ' . $http_code,
                'response' => $response
            );
        }

        wrs_log("Successfully deleted Hostaway reservation #" . $id);
        return array(
            'success' => true,
            'response' => $response
        );

    } catch (Exception $e) {
        wrs_log("Exception while deleting reservation #" . $id . ": " . $e->getMessage());
        return array(
            'success' => false,
            'error' => $e->getMessage()
        );
    }
  }

  public function update_product_reservation_calendars() {
    wrs_log("Updating product reservation calendars . . .");
	$calendar_table = new WodaabeReservationsCalendar_Table();
    $calendar_table->update_reservation_calendars();
  }

    public function unsync_external_reservation_calendar($id = null) {
        global $wpdb;

        // Set default values for $id and $link if not provided
        if ($id === null && isset($_POST['id'])) {
            $id = $_POST['id'];
        }

        $tablename = $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE;

        $wpdb->update(
          $tablename,
          array(
            'sync_url' => '',
          ),
          array('id' => $_POST["id"])
        );
        wrs_log("Syncing disabled for " . $id);

        wp_send_json_success($this->success(true));
    }

    /**
     * Save Hostaway listing ID for a product
     * This function is called via AJAX to save the Hostaway listing ID for a product
     */
    public function save_hostaway_listing_id() {
        global $wpdb;

        $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
        $listing_map_id = isset($_POST['listing_map_id']) && !empty($_POST['listing_map_id']) ? intval($_POST['listing_map_id']) : null;

        wrs_log("Saving Hostaway listing ID for product #" . $product_id . ": " . $listing_map_id);

        if (!$product_id) {
            wrs_log("ERROR: Invalid product ID");
            wp_send_json_error($this->error("Invalid product ID"));
            return;
        }

        // Get the current product data to check if this is a new listingMapId
        $current_product = $this->get_by_id(WODAABE_RESERVATIONS_PRODUCT_TABLE, $product_id);
        $is_new_listing_id = empty($current_product['listingMapId']) && !empty($listing_map_id);

        // Update the product with the new listingMapId
        $result = $wpdb->update(
            $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE,
            array('listingMapId' => $listing_map_id),
            array('id' => $product_id)
        );

        if ($result === false) {
            wrs_log("ERROR: Failed to update product with Hostaway listing ID: " . $wpdb->last_error);
            wp_send_json_error($this->error("Failed to update product: " . $wpdb->last_error));
            return;
        }

        wrs_log("Successfully saved Hostaway listing ID for product #" . $product_id);

        // If this is a new listingMapId being added, trigger the harmonization process
        if ($is_new_listing_id) {
            wrs_log("New Hostaway listing ID added. Triggering harmonization process...");
            $harmonization_result = $this->harmonize_reservations($product_id);

            if ($harmonization_result['success']) {
                wrs_log("Harmonization completed successfully");
            } else {
                wrs_log("Harmonization failed: " . ($harmonization_result['error'] ?? 'Unknown error'));
            }
        }

        wp_send_json_success($this->success(array(
            'product_id' => $product_id,
            'listing_map_id' => $listing_map_id,
            'harmonization_triggered' => $is_new_listing_id
        )));
    }

    public function sync_external_reservation_calendar($id = null, $link = null) {
        global $wpdb;

        // Set default values for $id and $link if not provided
        if ($id === null && isset($_POST['id'])) {
            $id = $_POST['id'];
        }
        if ($link === null && isset($_POST['link'])) {
            $link = $_POST['link'];
        }

        $tablename = $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE;

        if ($link === null && !isset($_POST['link']) || $link === '') {
            wrs_log("Syncing link not provided. Exiting...");
            return false;
        }

        wrs_log("sync_external_reservation_calendar start... ID: " . $id . " and link: " . $link);

        $product = $this->get_by_id(WODAABE_RESERVATIONS_PRODUCT_TABLE, $id);

        if (empty($product["sync_url"]) || $product["sync_url"] !== $link) {
            if (filter_var($link, FILTER_VALIDATE_URL)) {
                $wpdb->update(
                  $tablename,
                  array(
                    'sync_url' => $link,
                  ),
                  array('id' => $_POST["id"])
                );
            } else {
                wrs_log("Incorrect link provided. Exiting...");
            }
        } else {
            wrs_log("Link already saved: ". $link);
        }

        //$icalstring = file_get_contents('https://platform.hostaway.com/ical/1tWcYvET5hupafRo0OVEDKzXZ65tAJj2VBPIu8jzFBwWsICrS8QM4oaqvcyi3HGb/listings/178497.ics');
        $icalstring = file_get_contents($link);

        if (!$icalstring) {
            wrs_log($link . " is not a valid ical link");
            return false;
        }

        //$icalobj = new \ICalendarOrg\ZCiCal($icalstring);

        // Retrieve reservations
        $calendar = new WodaabeReservationsCalendar_Table();
        $reservations = $calendar->get_reservations_by_product($id);

        // Retrieve blocked periods
        $blocking_sql = $wpdb->prepare(
           "SELECT id, start_date, end_date, comment
           FROM {$wpdb->prefix}" . WODAABE_RESERVATIONS_PRODUCTS_BLOCK_TABLE . "
           WHERE product_id = %d",
           $product_id
        );
        $blocked_periods = $wpdb->get_results($blocking_sql, ARRAY_A);
        wrs_log(print_r($blocked, true));

        $icalobj = null;
        $eventcount = 0;
        $maxevents = 100;

        do
        {
        	$icalobj = new \ICalendarOrg\ZCiCal($icalstring, $maxevents, $eventcount);

        	wrs_log("Event count: " . $icalobj->countEvents());
        	wrs_log("Current event count: " . $eventcount);
        	wrs_log("Max events: " . $maxevents);

        	$node = $icalobj->getFirstEvent();
        	$count = 0;
            $eventCount = 0;
        	do
        	{


        	    if ($node === null) {
        	        break;
        	    }

        	    ++$count;
        	    wrs_log("Events count: " . $count);
        	    if($node->getName() == "VEVENT")
            	{
            	    ++$eventCount;
            	    wrs_log("VEVENT count: " . $eventCount);
            	    // Extract event data
                    $eventId = null;
                    $eventStart = null;
                    $eventEnd = null;
                    $eventSummary = null;
            		foreach($node->data as $key => $value)
            		{
            			if($key === "UID") {
                            $eventId = $value->getValues();
                        } elseif($key === "DTSTART") {
                            $eventStart = $value->getValues();
                        } elseif($key === "DTEND") {
                            $eventEnd = $value->getValues();
                        } elseif($key == "SUMMARY") {
                            $eventSummary = $value->getValues();
                            wrs_log("Evaluating: " . $value->getValues() . "\n");
                        }
            		}

            		// Check if the event exists in reservations array
                    $eventExists = false;
                    if (strpos($eventId, 'blocked-') === 0) { // Check if ID starts with 'blocked-'
                       $blockId = intval(str_replace('blocked-', '', $eventId)); // Extract number
                       wrs_log('Blocked period # ' . $eventId);
                       foreach ($blocked_periods as $period) {
                           if ($period['id'] === $blockId) {
                               $eventExists = true;
                               wrs_log("Blocked period #" . $blockId . " already exists in our database.");
                               break;
                           }
                       }
                    } else {
                       foreach($reservations as $reservation) {
                           foreach($reservation as $r) {
                               if($r['uuid'] === $eventId) {
                                   $eventExists = true;
                                   wrs_log($eventSummary . " already exists in our database.");
                                   break 2;
                               }
                           }
                       }
                    }

                    $blockOverlap = false;

                    // First check for blocked period overlaps using the SQL query
                    $blocking_sql = $wpdb->prepare(
                        "SELECT id, start_date, end_date, comment
                        FROM {$wpdb->prefix}" . WODAABE_RESERVATIONS_PRODUCTS_BLOCK_TABLE . "
                        WHERE product_id = %d
                        AND (
                            (start_date >= %s AND start_date <= %s)
                            OR (end_date >= %s AND end_date <= %s)
                            OR (start_date < %s AND end_date > %s)
                        )",
                        $product_id, $eventStart, $eventEnd, $eventStart, $eventEnd, $eventStart, $eventEnd
                    );
                    $overlapping_blocks = $wpdb->get_results($blocking_sql, ARRAY_A);
                    wrs_log(print_r($overlapping_blocks, true));

                    if (!empty($overlapping_blocks)) {
                        $blockOverlap = true;
                        wrs_log($eventSummary . " is overlapping with blocked period #" . $overlapping_blocks[0]['id']);
                    }

                    if (!$blockOverlap) {
                        // Check if any available reservation overlaps with the event period
                        $reservationOverlap = false;
                        foreach($reservations as $reservation) {
                            foreach($reservation as $r) {
                                $arrival = strtotime($r['date_arrive']);
                                $departure = strtotime($r['date_departure']);
                                $eventStartTime = strtotime($eventStart);
                                $eventEndTime = strtotime($eventEnd);

                                if(($arrival <= $eventStartTime && $departure >= $eventStartTime) || ($arrival <= $eventEndTime && $departure >= $eventEndTime)) {
                                    $reservationOverlap = true;
                                    wrs_log($eventSummary . " is overlapping with reservation #" . $r['id']);
                                    break ; // Exit both loops
                                }
                            }
                        }
                    }
                    wrs_log($eventSummary . ($eventExists ? " is already registered" : "") . ($reservationOverlap ? " and is overlapping with another reservation." : ""));
                    if (!$eventExists && !$reservationOverlap && !$blockOverlap) {
                        wrs_log("Saving external reservations : " . $eventSummary);
                        $res = $this->save_external_order($eventSummary, $eventStart, $eventEnd, $eventId, $id);
                        wrs_log("Saving external reservation result : " . $res);
                    }
                    $node = $icalobj->getNextEvent($node);
            	}
        	} while($node !== null);

        	$eventcount += $maxevents;

        } while($icalobj->countEvents() >= $eventcount);

        $this->update_product_reservation_calendars();

        wrs_log("Importation successful");
        return wp_send_json_success($this->success(true));
    }

    /**
     * Find existing reservation by dates and product
     * Used to prevent duplicate reservations
     */
    private function find_existing_reservation($arrive, $departure, $product_id, $uuid = null) {
        global $wpdb;

        $reservation_table = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
        $reservation_item_table = $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE;

        // First try to find by UUID if provided
        if ($uuid) {
            $sql = $wpdb->prepare(
                "SELECT r.*
                 FROM {$reservation_table} r
                 WHERE r.uuid = %s",
                $uuid
            );

            $reservation = $wpdb->get_row($sql, ARRAY_A);
            if ($reservation) {
                wrs_log("Found existing reservation #" . $reservation['id'] . " with matching UUID: " . $uuid);
                return $reservation;
            }
        }

        // Then try to find by dates and product
        $sql = $wpdb->prepare(
            "SELECT r.*
             FROM {$reservation_table} r
             JOIN {$reservation_item_table} ri ON r.id = ri.reservation_id
             WHERE ri.product_id = %d
             AND r.date_arrive = %s
             AND r.date_departure = %s
             AND r.status != 'Canceled'",
            $product_id, $arrive, $departure
        );

        $reservation = $wpdb->get_row($sql, ARRAY_A);
        if ($reservation) {
            wrs_log("Found existing reservation #" . $reservation['id'] . " with matching dates and product");
            return $reservation;
        }

        return null;
    }

    public function save_external_order($name, $arrive, $departure, $uuid, $product_id) {
        wrs_log("Saving reservation with data: " . print_r([$name, $arrive, $departure, $uuid, $product_id], true));
        global $wpdb;

        try {
          $wpdb->show_errors();

          // Check if this is a duplicate reservation
          $existing_reservation = $this->find_existing_reservation($arrive, $departure, $product_id, $uuid);
          if ($existing_reservation) {
              wrs_log("Duplicate reservation detected. Not creating a new one.");
              return $existing_reservation['id']; // Return the existing reservation ID
          }

          // Get the product details to check if it has a listingMapId
          $product = $this->get_by_id(WODAABE_RESERVATIONS_PRODUCT_TABLE, $product_id);

          // If the product has a listingMapId, check availability in Hostaway first
          if (!empty($product['listingMapId'])) {
            wrs_log("Product has Hostaway listing ID: " . $product['listingMapId'] . ". Checking availability in Hostaway...");

            // Check if this reservation already exists in Hostaway
            $hostaway_id = $this->get_hostaway_reservation_id($arrive, $departure, $product['listingMapId']);

            if ($hostaway_id) {
                wrs_log("Reservation already exists in Hostaway with ID: " . $hostaway_id);
                // We'll create the local reservation and then store the mapping
            } else {
                // Check availability in Hostaway
                $availability_result = $this->check_hostaway_availability($product['listingMapId'], $arrive, $departure);

                if (!$availability_result['available']) {
                  wrs_log("Dates not available in Hostaway: " . ($availability_result['error'] ?? 'Unknown error'));
                  return false; // Don't create the reservation if dates are not available in Hostaway
                }

                wrs_log("Dates are available in Hostaway. Proceeding with reservation creation.");
            }
          }

          $wpdb->query('START TRANSACTION');

          $person_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE;
          $payment_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE;
          $reservation_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
          $reservation_item_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE;

            // Check if a record with the given first_name already exists
            $existing_record = $wpdb->get_row(
                $wpdb->prepare("SELECT * FROM $person_table_name WHERE first_name = %s", $name)
            );
            $personId = null;

            // If no existing record found, insert the new record
            if (!$existing_record) {
                $wpdb->insert(
                    $person_table_name,
                    array(
                        'first_name' => $name,
                    )
                );
                $personId = $wpdb->insert_id;
                wrs_log("Created new person with Id : " . $personId);
            } else {
                wrs_log($name . " already exists in the person's table");
                // Record exists, assign its ID to a variable
                $personId = $existing_record->id;
                wrs_log("Using known person with Id : " . $personId);
            }

          $wpdb->insert($payment_table_name,
            array(
              'transaction_id' => 'EXTERNAL',
              'status' => 'COMPLETED',
            )
          );
          $payId = $wpdb->insert_id;
          wrs_log("Created new payment with Id : " . $payId);

          $wpdb->insert($reservation_table_name,
            array(
              'date_arrive' => $arrive,
              'date_departure' => $departure,
              'uuid' => $uuid,
              'status' => 'Active',
              'person_id' => $personId,
              'payment_id' => $payId,
              'notif_status' => 'ARCHIVED',
              'is_external' => 1,
            )
          );
          $reservationId = $wpdb->insert_id;
          wrs_log("Created new reservation with Id : " . $reservationId);

          $wpdb->insert($reservation_item_table_name,
            array(
              'product_id' => intval($product_id),
              'reservation_id' => intval($reservationId),
            )
          );
          $itemsId = $wpdb->insert_id;
          wrs_log("Created new reservation items with Id : " . $itemsId);
          wrs_log($name . "'s reservation created successfully");
          wrs_log($wpdb->last_error, 'INFO');
          $wpdb->query('COMMIT');
          $this->update_product_reservation_calendars();

          // Create the reservation in Hostaway if the product has a listingMapId
          if (!empty($product['listingMapId']) && !$hostaway_id) {
            $hostaway_result = $this->create_hostaway_reservation($reservationId);
            if ($hostaway_result && $hostaway_result['success']) {
              wrs_log("Successfully created reservation in Hostaway with ID: " . $hostaway_result['hostaway_id']);

              // Store the mapping for future lookups
              $config_key = 'hostaway_reservation_map_' . $hostaway_result['hostaway_id'];
              $this->save_configuration($config_key, $reservationId);
            } else {
              wrs_log("Failed to create reservation in Hostaway: " . ($hostaway_result['error'] ?? 'Unknown error'));
              // Continue with the local reservation even if Hostaway creation fails
            }
          } else if (!empty($product['listingMapId']) && $hostaway_id) {
            // Store the mapping for future lookups
            $config_key = 'hostaway_reservation_map_' . $hostaway_id;
            $this->save_configuration($config_key, $reservationId);
            wrs_log("Stored mapping between local reservation #" . $reservationId . " and Hostaway reservation #" . $hostaway_id);
          }

          return $reservationId;
        } catch (\Throwable $th) {
          wrs_log($th->__toString(), 'ERROR');
          $wpdb->query('ROLLBACK');
          return false;
        }

        return false;
      }

    public function get_promo_details() {
        wrs_log("Promo code: ");
        wrs_log($_POST['promocodevalue']);

        $code = !empty($_POST['promocodevalue']) ? sanitize_text_field($_POST['promocodevalue']) : null;

        wrs_log("Code: " . $code);
        wrs_log("Server: Retrieving promo details");

        global $wpdb;
        $promo_table = $wpdb->prefix . WODAABE_RESERVATIONS_PROMOTIONAL_CODE_TABLE;
        $promo_products_table = $wpdb->prefix . WODAABE_RESERVATIONS_PROMOTIONAL_CODE_PRODUCTS_TABLE;

        // Fetch promo code details
        $promo_details = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$promo_table} WHERE code = %s",
            $code
        ), ARRAY_A);

        if ($promo_details) {
            // Fetch associated product IDs
            $product_ids = $wpdb->get_col($wpdb->prepare(
                "SELECT product_id FROM {$promo_products_table} WHERE promo_code_id = %d",
                $promo_details['id']
            ));

            // Add product IDs to the promo details
            $promo_details['product_ids'] = $product_ids;
        }

        wrs_log("Promo details: ");
        wrs_log($promo_details);

        return wp_send_json_success($this->success($promo_details));
    }

    /**
     * Get all blocked periods for all products.
     *
     * @return array An array of blocked periods with product ID, start date, and end date.
     */
    public function get_blocked_periods() {
        global $wpdb;

        // Define the product block table name
        $product_block_table = $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCTS_BLOCK_TABLE;

        // Fetch all blocked periods from the database
        $blocked_periods = $wpdb->get_results(
            "SELECT product_id, start_date, end_date, comment
             FROM $product_block_table",
            ARRAY_A  // Return results as an associative array
        );

        // Return blocked periods as an array with product ID, start and end dates
        return wp_send_json_success($this->success($blocked_periods));
    }

    function fetchPriceLabsData() {
        $url = "https://api.pricelabs.co/v1/listing_prices/";

        $data = array(
            'listings' => array(
                array(
                    'id' => $_POST['id'],
                    'pms' => $_POST['pms'],
                    'dateFrom' => $_POST['dateFrom'],
                    'dateTo' => $_POST['dateTo'],
                    'reason' => $_POST['reason']
                )
            )
        );

        $data_string = json_encode($data);

        // Initialize a CURL session
        $ch = curl_init($url);

        // Setup CURL options
        //curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        //curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        //curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'X-API-Key: 3mcNq0270Bdbc5gzff5XJC3rpQJFiFr2G7GCAYJ3',
            'Content-Length: ' . strlen($data_string)
        ]);
        // Execute the CURL command
        $response = curl_exec($ch);
        // Check for any CURL errors
        if (curl_errno($ch)) {
            $error_msg = curl_error($ch);
            wrs_log($error_msg);
            curl_close($ch);
            return json_encode(['error' => $error_msg]);
        }

        // Close the CURL session
        curl_close($ch);

        // Return the data from the API
        return wp_send_json_success($this->success($response));
    }

    function getPriceLabsListings() {
        $products_table = new WodaabeReservationsProducts_Table();
        $response = $products_table->get_pricelabs_listings();
        return wp_send_json_success($this->success($response));
    }

  /*public function sent_code() {
    global $wpdb;

    $code = random_int(1000, 9999);

    $items = array(
      "Your Email" => $_POST['email'],
      "Your code" => $code,
    );
    $wpdb->insert('wp_wrs_otp_codes',
      array(
        'code' => $code,
        'email' => $_POST['email'],
      )
    );
    $this->send_otp_mail($items);

    wp_send_json_success($this->success($itemprint_r($icalobj, trues));
  }*/

  public function verify_code() {
    global $wpdb;

    $code = $_POST['code'];

    $table = 'wp_wrs_otp_codes';
    $result = $wpdb->get_row("SELECT * FROM {$table} WHERE code = " . "'$code'");

    if ($result) {
      if ($result->code == $code) {
        wp_send_json_success($this->success($result));
      }
    }
    wp_send_json_error($this->error("Une erreur est survenue! Merci de reéssayer plus tard."));
  }

  public function update_payment_status() {
    global $wpdb;

    $res = $this->get_reservation($_POST['id']);
    if ($res) {
      $payment_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE;
      $wpdb->update(
        $payment_table_name,
        array(
          'status' => $_POST['status'],
          'transaction_id' => $_POST['transaction_id'],
        ),
        array('id' => $res->payment_id)
      );
      if ("COMPLETED" == $_POST['status']) {
        $table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
        $wpdb->update(
          $table_name,
          array(
            'status' => 'Active',
          ),
          array('id' => $_POST["id"])
        );
      }
      if (!empty($_POST['transaction_id'])) {
        $this->send_mails($res);
      }
    }

    wp_send_json_success($this->success($res));
  }

  public function update_stripe_payment_status($reservation_id, $transaction_id) {
    global $wpdb;

    $res = $this->get_reservation(intval($reservation_id));
    if ($res) {
      // Get the checkout session to retrieve the amount
      try {
        $stripe = new \Stripe\StripeClient($_ENV['STRIPE_API_SECRET_KEY']);
        $payment_intent = $stripe->paymentIntents->retrieve($transaction_id);
        $amount = $payment_intent->amount / 100; // Convert from cents to actual currency
        wrs_log("Retrieved payment amount from Stripe: " . $amount);
      } catch (\Exception $e) {
        wrs_log("Error retrieving payment details from Stripe: " . $e->getMessage());
        $amount = null;
      }

      $payment_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE;
      $update_data = array(
        'status' => 'COMPLETED',
        'transaction_id' => $transaction_id,
      );

      // Add amount to update data if we were able to retrieve it
      if ($amount !== null) {
        $update_data['amount'] = $amount;
      }

      $wpdb->update(
        $payment_table_name,
        $update_data,
        array('id' => $res->payment_id)
      );

      $table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
      $wpdb->update(
        $table_name,
        array(
          'status' => 'Active',
        ),
        array('id' => $res->id)
      );

      if (!empty($transaction_id)) {
        $this->sendMail("Reservation #" . $res->id, 'mail_administrateur', $res->id, "<EMAIL>"); //, "<EMAIL>");
      } else {
        wrs_log("Stripe transaction id is empty: " . $transaction_id);
      }
    }

    wp_send_json_success($this->success($res));
  }

  public function create_ical_export_endpoint() {
    // Get the current site URL dynamically
    $site_url = site_url();
    $webhook_url = $site_url . '/wp-json/wodaabe_reservations/v1/ical-export';

    //wrs_log("Registering route " . $webhook_url);
    register_rest_route('wodaabe_reservations/v1', '/ical-export', array(
        'methods' => 'GET',
        'callback' => array($this, 'handle_ical_export'),
        'permission_callback' => '__return_true',
    ));
  }

  public function create_ical_sync_endpoint() {
    // Get the current site URL dynamically
    $site_url = site_url();
    $webhook_url = $site_url . '/wp-json/wodaabe_reservations/v1/ical-sync';

    //wrs_log("Registering route " . $webhook_url);
    register_rest_route('wodaabe_reservations/v1', '/ical-sync', array(
        'methods' => 'GET',
        'callback' => array($this, 'handle_ical_sync'),
        'permission_callback' => '__return_true',
    ));
  }
  /*
  public function create_price_sync_endpoint() {
    // Get the current site URL dynamically
    $site_url = site_url();
    $webhook_url = $site_url . '/wp-json/wodaabe_reservations/v1/price-sync';

    //wrs_log("Registering route " . $webhook_url);
    register_rest_route('wodaabe_reservations/v1', '/price-sync', array(
        'methods' => 'GET',
        'callback' => array($this, 'handle_price_sync'),
        'permission_callback' => '__return_true',
    ));
  }

  public function handle_price_sync() {
      $products_table = new WodaabeReservationsProducts_Table();
      $products_table->sync_prices();
  }
  */

    public function handle_ical_sync() {
        wrs_log("handling Ical sync...");
        error_log("handling Ical sync...");
        echo("handling Ical sync...\n");
        $calendar = new WodaabeReservationsCalendar_Table();
        $products = $calendar->get_products();
        //echo print_r($products, true);
        //wrs_log(print_r($products, true));

        foreach ($products as $product) {
            if ($product["sync_url"] !== null && filter_var($product["sync_url"], FILTER_VALIDATE_URL)) {
                $str = $product["title"] . "'s sync_url is valid";
                wrs_log($str);
                $this->sync_external_reservation_calendar($product["id"], $product["sync_url"]);
            }
        }
        wrs_log("HIS end...");
        echo "his end\n";
    }

  public function handle_ical_export() {
	$calendar = new WodaabeReservationsCalendar_Table();
	$reservations = $calendar->get_full_reservations($calendar->get_reservations());
	wrs_log(print_r($reservations, true));
    $calendar->generate_icalendar_file($reservations);
  }

  public function create_stripe_webhook() {
    // Get the current site URL dynamically
    $site_url = site_url();
    $webhook_url = $site_url . '/wp-json/wodaabe_reservations/v1/stripe-webhook';

    //wrs_log("Registering route " . $webhook_url);
    register_rest_route('wodaabe_reservations/v1', '/stripe-webhook', array(
        'methods' => 'POST',
        'callback' => array($this, 'handle_stripe_webhook'),
        'permission_callback' => '__return_true',
    ));
  }

  public function create_email_reminders_endpoint() {
    // Get the current site URL dynamically
    $site_url = site_url();
    $webhook_url = $site_url . '/wp-json/wodaabe_reservations/v1/handle-email-reminders';

    //wrs_log("Registering route " . $webhook_url);
    register_rest_route('wodaabe_reservations/v1', '/handle-email-reminders', array(
        'methods' => 'POST',
        'callback' => array($this, 'handle_email_reminders'),
        'permission_callback' => '__return_true',
    ));
  }

  public function create_hostaway_webhook() {
    // Get the current site URL dynamically
    $site_url = site_url();
    $webhook_url = $site_url . '/wp-json/wodaabe_reservations/v1/hostaway-webhook';

    //wrs_log("Registering route " . $webhook_url);
    register_rest_route('wodaabe_reservations/v1', '/hostaway-webhook', array(
        'methods' => 'POST, GET',  // Allow both POST and GET for testing
        'callback' => array($this, 'handle_hostaway_webhook'),
        'permission_callback' => '__return_true',
    ));
  }

  public function handle_hostaway_webhook(WP_REST_Request $request) {
    // Check if this is a GET request (for testing)
    if ($request->get_method() === 'GET') {
      wrs_log("Received GET request to Hostaway webhook endpoint (test mode)");
      return new WP_REST_Response('Hostaway webhook endpoint is working. Please use POST for actual webhooks.', 200);
    }

    // Get the webhook payload
    $payload = $request->get_body();
    $data = json_decode($payload, true);

    wrs_log("Received Hostaway webhook: " . print_r($data, true));

    // Verify the webhook is from Hostaway (you might want to add more security here)
    if (!isset($data['event'])) {
      wrs_log("Invalid webhook payload - missing event type");
      return new WP_REST_Response('Invalid webhook payload', 400);
    }

    // Extract the listingMapId from the data
    $listing_map_id = isset($data['data']['listingMapId']) ? $data['data']['listingMapId'] : null;

    if (!$listing_map_id) {
        wrs_log("No listingMapId found in webhook data");
        return new WP_REST_Response('No listingMapId found', 400);
    }

    // Find the product with this listingMapId
    global $wpdb;
    $product_table = $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE;
    $product = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $product_table WHERE listingMapId = %d", $listing_map_id),
        ARRAY_A
    );

    if (!$product) {
        wrs_log("No product found with listingMapId: " . $listing_map_id);
        return new WP_REST_Response('No matching product found', 404);
    }

    // Handle different event types
    $event_type = $data['event'];

    try {
      // We handle message.received, reservation.created, reservation.updated, and reservation.canceled
      switch ($event_type) {
        case 'message.received':
          wrs_log("Received message from Hostaway. No action needed.");
          break;

        case 'reservation.created':
          $this->handle_hostaway_reservation_created($data);
          break;

        case 'reservation.updated':
          // Check if the status is 'cancelled' in the updated reservation
          if (isset($data['data']['status']) && $data['data']['status'] === 'cancelled') {
            wrs_log("Reservation updated with status 'cancelled'. Handling as cancellation.");
            $this->handle_hostaway_reservation_canceled($data);
          } else if (isset($data['data']['status']) && $data['data']['status'] === 'modified') {
            wrs_log("Reservation updated with status 'modified'. Handling as update.");
            $this->handle_hostaway_reservation_updated($data);
          } else if (isset($data['data']['status']) && $data['data']['status'] === 'new') {
            wrs_log("Reservation updated with status 'new'. Handling as creation.");
            $this->handle_hostaway_reservation_created($data);
          } else {
            // For other status updates, handle normally
            wrs_log("Reservation updated with status: " . ($data['data']['status'] ?? 'unknown') . ". Handling as generic update.");
            $this->handle_hostaway_reservation_updated($data);
          }
          break;

        case 'reservation.canceled':
          wrs_log("Received reservation.canceled event. Handling cancellation.");
          $this->handle_hostaway_reservation_canceled($data);
          break;

        default:
          wrs_log("Unhandled event type: " . $event_type . ". Only handling message.received, reservation.created, reservation.updated, and reservation.canceled.");
          break;
      }
    } catch (\Throwable $th) {
      wrs_log("Error handling Hostaway webhook: " . $th->getMessage());
      wrs_log($th->getTraceAsString());
      // Continue processing and return 200 to acknowledge receipt
    }

    return new WP_REST_Response('Webhook Received', 200);
  }

  private function handle_hostaway_reservation_created($data) {
    wrs_log("Processing Hostaway reservation created event");

    // Extract reservation data - it's in the 'data' field of the webhook payload
    $reservation = $data['data'];

    // Check if this reservation already exists in our system
    // We can use the Hostaway reservation ID to check
    $hostaway_id = $reservation['id'];
    $existing_reservation_id = $this->find_reservation_by_hostaway_id($hostaway_id);

    if ($existing_reservation_id) {
      wrs_log("Reservation already exists in our system with ID: " . $existing_reservation_id);
      return;
    }

    // Create a new reservation in our system
    $this->create_reservation_from_hostaway($reservation);
  }

  private function handle_hostaway_reservation_updated($data) {
    wrs_log("Processing Hostaway reservation updated event");

    // Extract reservation data - it's in the 'data' field of the webhook payload
    $reservation = $data['data'];
    $hostaway_id = $reservation['id'];

    // Check if the status is 'cancelled'
    if (isset($reservation['status']) && $reservation['status'] === 'cancelled') {
      wrs_log("Hostaway reservation status is 'cancelled'. Handling as cancellation.");
      $this->handle_hostaway_reservation_canceled($data);
      return;
    }

    // Find the corresponding reservation in our system
    $local_reservation_id = $this->find_reservation_by_hostaway_id($hostaway_id);

    if (!$local_reservation_id) {
      wrs_log("Reservation not found in our system. Creating new reservation.");
      $this->create_reservation_from_hostaway($reservation);
      return;
    }

    // Update the existing reservation
    $this->update_reservation_from_hostaway($local_reservation_id, $reservation);
  }

  private function handle_hostaway_reservation_canceled($data) {
    wrs_log("Processing Hostaway reservation canceled event");

    // Extract reservation data - it's in the 'data' field of the webhook payload
    $reservation = $data['data'];
    $hostaway_id = $reservation['id'];

    // Find the corresponding reservation in our system
    $local_reservation_id = $this->find_reservation_by_hostaway_id($hostaway_id);

    if (!$local_reservation_id) {
      wrs_log("Reservation not found in our system. Nothing to cancel.");
      return;
    }

    // Cancel the reservation in our system
    $this->cancel_reservation($local_reservation_id);

    // Delete the local reservation after cancelling it
    global $wpdb;
    wrs_log("Deleting local reservation #" . $local_reservation_id . " after cancellation");

    $wpdb->delete(
      $wpdb->prefix . WODAABE_RESERVATIONS_TABLE,
      ['id' => $local_reservation_id],
      ['%d']
    );

    // Update calendars
    $this->update_product_reservation_calendars();
  }

  private function find_reservation_by_hostaway_id($hostaway_id) {
    wrs_log("Searching for local reservation with Hostaway ID: " . $hostaway_id);

    // Look up the mapping in our configuration table
    $config_key = 'hostaway_reservation_map_' . $hostaway_id;
    $local_id = $this->get_configuration($config_key);

    if (!empty($local_id)) {
      wrs_log("Found local reservation #" . $local_id . " mapped to Hostaway ID " . $hostaway_id . " in configuration");
      return $local_id;
    }

    // If not found in configuration, try to find by matching Hostaway reservation data
    $token = $this->get_hostaway_token();
    if (empty($token)) {
      wrs_log("ERROR: Cannot find reservation - Authentication token is null");
      return null;
    }

    try {
      // Get the reservation details from Hostaway API
      $curl = curl_init();
      curl_setopt_array($curl, array(
        CURLOPT_URL => "https://api.hostaway.com/v1/reservations/" . $hostaway_id,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "GET",
        CURLOPT_HTTPHEADER => array(
          "Authorization: Bearer " . $token,
          "Cache-control: no-cache"
        ),
      ));

      $response = curl_exec($curl);
      $err = curl_error($curl);
      curl_close($curl);

      if ($err) {
        wrs_log("ERROR: CURL error while finding reservation: " . $err);
        return null;
      }

      $data = json_decode($response, true);
      if (!isset($data['result'])) {
        wrs_log("ERROR: Invalid response from Hostaway API");
        wrs_log("Response: " . $response);
        return null;
      }

      $hostaway_reservation = $data['result'];

      // Check if we have the necessary data to match the reservation
      if (!isset($hostaway_reservation['listingMapId']) ||
          !isset($hostaway_reservation['arrivalDate']) ||
          !isset($hostaway_reservation['departureDate'])) {
        wrs_log("ERROR: Missing required fields in Hostaway reservation data");
        return null;
      }

      // Try to match by reservation ID from Hostaway
      if (isset($hostaway_reservation['reservationId']) || isset($hostaway_reservation['channelReservationId'])) {
        global $wpdb;
        $reservation_table = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;

        // Try to match by UUID using reservationId or channelReservationId
        $reservation_id = $hostaway_reservation['reservationId'] ?? $hostaway_reservation['channelReservationId'] ?? null;

        if ($reservation_id) {
          $query = $wpdb->prepare(
            "SELECT id FROM {$reservation_table} WHERE uuid = %s",
            $reservation_id
          );

          wrs_log("Executing SQL query to match by reservation ID: " . $query);
          $local_id = $wpdb->get_var($query);

          if ($local_id) {
            wrs_log("Found local reservation #" . $local_id . " with UUID matching Hostaway reservation ID");

            // Save the mapping for future lookups
            $this->save_configuration($config_key, $local_id);

            return $local_id;
          }
        }
      }

      // If still not found, try to match by dates and listing
      $listing_id = $hostaway_reservation['listingMapId'];
      $arrival_date = $hostaway_reservation['arrivalDate'];
      $departure_date = $hostaway_reservation['departureDate'];

      wrs_log("Trying to match by dates and listing: Listing ID " . $listing_id .
              ", Arrival: " . $arrival_date . ", Departure: " . $departure_date);

      // Find the product with this listing ID
      global $wpdb;
      $product_table = $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE;
      $product_query = $wpdb->prepare(
        "SELECT id FROM {$product_table} WHERE listingMapId = %s",
        $listing_id
      );

      wrs_log("Executing product SQL query: " . $product_query);
      $product_id = $wpdb->get_var($product_query);

      if (!$product_id) {
        wrs_log("No product found with listingMapId: " . $listing_id);
        return null;
      }

      // Find reservations for this product with matching dates
      $reservation_table = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
      $reservation_item_table = $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE;
      $date_query = $wpdb->prepare(
        "SELECT r.id
         FROM {$reservation_table} r
         JOIN {$reservation_item_table} ri ON r.id = ri.reservation_id
         WHERE ri.product_id = %d
         AND r.date_arrive = %s
         AND r.date_departure = %s
         LIMIT 1",
        $product_id, $arrival_date, $departure_date
      );

      wrs_log("Executing date match SQL query: " . $date_query);
      $date_match_id = $wpdb->get_var($date_query);

      if ($date_match_id) {
        wrs_log("Found local reservation #" . $date_match_id . " with matching dates and product");

        // Save the mapping for future lookups
        $this->save_configuration($config_key, $date_match_id);

        return $date_match_id;
      }

      // If we have guest information, try to match by guest email and dates
      if (!empty($hostaway_reservation['guestEmail'])) {
        $person_table = $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE;
        $email_query = $wpdb->prepare(
          "SELECT r.id
           FROM {$reservation_table} r
           JOIN {$person_table} p ON r.person_id = p.id
           WHERE p.email = %s
           AND r.date_arrive = %s
           AND r.date_departure = %s
           LIMIT 1",
          $hostaway_reservation['guestEmail'], $arrival_date, $departure_date
        );

        wrs_log("Executing email match SQL query: " . $email_query);
        $email_match_id = $wpdb->get_var($email_query);

        if ($email_match_id) {
          wrs_log("Found local reservation #" . $email_match_id . " with matching guest email and dates");

          // Save the mapping for future lookups
          $this->save_configuration($config_key, $email_match_id);

          return $email_match_id;
        }
      }

    } catch (\Exception $e) {
      wrs_log("ERROR: Exception while finding reservation by Hostaway ID: " . $e->getMessage());
    }

    wrs_log("No matching local reservation found for Hostaway ID: " . $hostaway_id);
    return null;
  }





  private function create_reservation_from_hostaway($hostaway_reservation) {
    global $wpdb;
    wrs_log("Creating local reservation from Hostaway data: " . print_r($hostaway_reservation, true));

    try {
      // Check if the listingMapId exists in the hostaway_reservation data
      if (!isset($hostaway_reservation['listingMapId'])) {
        wrs_log("ERROR: Hostaway reservation data does not contain listingMapId. Full data: " . print_r($hostaway_reservation, true));
        return false;
      }

      // Log the listingMapId we're looking for
      wrs_log("Looking for product with Hostaway listingMapId: " . $hostaway_reservation['listingMapId']);

      // Find the product with matching listingMapId FIRST before creating anything
      $product_id = null;
      $sql_query = $wpdb->prepare(
        "SELECT id FROM {$wpdb->prefix}" . WODAABE_RESERVATIONS_PRODUCT_TABLE . " WHERE listingMapId = %d",
        $hostaway_reservation['listingMapId']
      );

      // Log the SQL query
      wrs_log("SQL query to find matching product: " . $sql_query);

      $products_with_listing_map_id = $wpdb->get_results($sql_query, ARRAY_A);

      if (empty($products_with_listing_map_id)) {
        wrs_log("ERROR: Cannot find product with listingMapId: " . $hostaway_reservation['listingMapId']);
        wrs_log("Reservation cannot be created because no matching product was found");
        return false;
      }

      // Use the first product that matches the listingMapId
      $product_id = $products_with_listing_map_id[0]['id'];
      wrs_log("Found product with ID: " . $product_id . " matching listingMapId: " . $hostaway_reservation['listingMapId']);

      // Now that we've confirmed a matching product exists, proceed with creating the reservation
      $wpdb->show_errors();
      $wpdb->query('START TRANSACTION');

      $person_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE;
      $payment_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE;
      $reservation_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
      $reservation_item_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE;

      // Prepare guest name - combine guestName, guestFirstName, and guestLastName
      $first_name = '';
      $last_name = '';

      // Combine guestFirstName and guestName for first_name if both are available
      if (!empty($hostaway_reservation['guestFirstName']) && !empty($hostaway_reservation['guestName'])) {
        $first_name = $hostaway_reservation['guestFirstName'] . ' ' . $hostaway_reservation['guestName'];
      }
      // If only guestFirstName is available, use it for first_name
      else if (!empty($hostaway_reservation['guestFirstName'])) {
        $first_name = $hostaway_reservation['guestFirstName'];
      }
      // If only guestName is available, use it for first_name
      else if (!empty($hostaway_reservation['guestName'])) {
        $first_name = $hostaway_reservation['guestName'];
      }

      // If guestLastName is available, use it for last_name
      if (!empty($hostaway_reservation['guestLastName'])) {
        $last_name = $hostaway_reservation['guestLastName'];
      }

      // Check if a similar person already exists using multiple fields
      $existing_person = null;

      // Build a query to find existing person based on available data
      $query_conditions = array();
      $query_params = array();

      // Check by email if available
      if (!empty($hostaway_reservation['guestEmail'])) {
        $query_conditions[] = "email = %s";
        $query_params[] = $hostaway_reservation['guestEmail'];
      }

      // Check by phone if available
      if (!empty($hostaway_reservation['phone'])) {
        $query_conditions[] = "phone = %s";
        $query_params[] = $hostaway_reservation['phone'];
      }

      // Check by name combination if both first and last name are available
      if (!empty($first_name) && !empty($last_name)) {
        $query_conditions[] = "(first_name = %s AND last_name = %s)";
        $query_params[] = $first_name;
        $query_params[] = $last_name;
      }

      // Only proceed with the query if we have conditions
      if (!empty($query_conditions)) {
        $sql = "SELECT id FROM {$person_table_name} WHERE " . implode(" OR ", $query_conditions);
        $prepared_sql = $wpdb->prepare($sql, ...$query_params);
        wrs_log("Looking for existing person with query: " . $prepared_sql);
        $existing_person = $wpdb->get_row($prepared_sql);
      }

      if ($existing_person) {
        $person_id = $existing_person->id;
        wrs_log("Using existing person with ID: " . $person_id);

        // Update the person's information
        $wpdb->update(
          $person_table_name,
          array(
            'first_name' => $first_name,
            'last_name' => $last_name,
            'phone' => $hostaway_reservation['phone'] ?? '',
            'country' => $hostaway_reservation['guestCountry'] ?? '',
            'comment' => $hostaway_reservation['guestNote'] ?? '',
            'is_guess' => 0,
          ),
          array('id' => $person_id)
        );
      } else {
        // Create person record
        $person_data = array(
          'first_name' => $first_name,
          'last_name' => $last_name,
          'email' => $hostaway_reservation['guestEmail'] ?? '',
          'phone' => $hostaway_reservation['phone'] ?? '',
          'country' => $hostaway_reservation['guestCountry'] ?? '',
          'comment' => $hostaway_reservation['guestNote'] ?? '',
          'is_guess' => 0,
        );

        wrs_log("Inserting new person with data: " . print_r($person_data, true));
        $wpdb->insert($person_table_name, $person_data);

        // Get the actual inserted ID using a direct query and verify it
        $person_id = $wpdb->insert_id;

        // Double-check that we got a valid ID
        if (!$person_id || $person_id <= 0) {
          // Try to get it with a direct query
          $person_id = $wpdb->get_var("SELECT LAST_INSERT_ID()");
          wrs_log("Had to use LAST_INSERT_ID() to get person ID: " . $person_id);
        }

        // Verify the person was actually created
        $person_check = $wpdb->get_row($wpdb->prepare(
          "SELECT * FROM {$person_table_name} WHERE id = %d",
          $person_id
        ));

        if (!$person_check) {
          wrs_log("ERROR: Failed to create person record. Database error: " . $wpdb->last_error);
          throw new \Exception("Failed to create person record");
        }

        wrs_log("Created new person with ID: " . $person_id);
      }

      // Get the total price from the reservation
      $total_price = !empty($hostaway_reservation['totalPrice']) ? floatval($hostaway_reservation['totalPrice']) : 0;

      // Create payment record - ensure amount is properly formatted as a decimal
      $formatted_amount = number_format((float)$total_price, 2, '.', '');
      wrs_log("Formatting payment amount: " . $total_price . " to " . $formatted_amount);

      $payment_data = array(
        'transaction_id' => 'HOSTAWAY-' . $hostaway_reservation['id'],
        'status' => 'COMPLETED',
        'amount' => $formatted_amount,
      );

      wrs_log("Inserting payment with data: " . print_r($payment_data, true));

      // Insert the payment record and get the ID
      $payment_insert_result = $wpdb->insert($payment_table_name, $payment_data);

      if ($payment_insert_result === false) {
        wrs_log("ERROR: Failed to insert payment record. Database error: " . $wpdb->last_error);
        throw new \Exception("Failed to insert payment record");
      }

      // Get the ID of the inserted payment record
      $payment_id = $wpdb->insert_id;

      // If insert_id doesn't work, try a direct query
      if (!$payment_id || $payment_id <= 0) {
        $payment_id = $wpdb->get_var("SELECT LAST_INSERT_ID()");
        wrs_log("Had to use LAST_INSERT_ID() to get payment ID: " . $payment_id);
      }

      // Verify the payment was inserted correctly with a separate query
      $payment_check = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$payment_table_name} WHERE id = %d",
        $payment_id
      ));

      if (!$payment_check) {
        wrs_log("ERROR: Payment record not found after insertion. Database error: " . $wpdb->last_error);
        throw new \Exception("Payment record not found after insertion");
      }

      // Double-check that the amount was saved correctly
      if ($payment_check->amount != $formatted_amount) {
        wrs_log("WARNING: Payment amount mismatch. Expected: " . $formatted_amount . ", Actual: " . $payment_check->amount);

        // Try to update the payment record with the correct amount
        $wpdb->update(
          $payment_table_name,
          array('amount' => $formatted_amount),
          array('id' => $payment_id)
        );

        wrs_log("Attempted to fix payment amount with update query");
      }

      wrs_log("Created new payment with ID: " . $payment_id . " with amount: " . $formatted_amount);
      wrs_log("Payment verification: " . print_r($payment_check, true));

      // Determine adult and child counts
      $adult_count = 0;
      $child_count = 0;

      // If adults field is available and not empty
      if (isset($hostaway_reservation['adults']) && $hostaway_reservation['adults'] !== '') {
        $adult_count = intval($hostaway_reservation['adults']);
      }

      // If children field is available and not empty
      if (isset($hostaway_reservation['children']) && $hostaway_reservation['children'] !== '') {
        $child_count = intval($hostaway_reservation['children']);
      }

      // If neither adults nor children are specified but numberOfGuests is available
      if (($adult_count + $child_count) == 0 && !empty($hostaway_reservation['numberOfGuests'])) {
        // Assume all guests are adults if not specified
        $adult_count = intval($hostaway_reservation['numberOfGuests']);
      }

      // Create reservation record
      $reservation_uuid = wrs_uuid();
      $reservation_data = array(
        'date_arrive' => $hostaway_reservation['arrivalDate'],
        'date_departure' => $hostaway_reservation['departureDate'],
        'uuid' => $reservation_uuid,
        'adult_count' => $adult_count,
        'child_count' => $child_count,
        'status' => 'Active',
        'person_id' => $person_id,
        'language_id' => 1, // Default language
        'currency_id' => 1, // Default currency
        'payment_id' => $payment_id,
        'notif_status' => 'ARCHIVED', // Set to ARCHIVED to prevent email reminders to Hostaway chat system
        'is_external' => 1, // Mark as external reservation
      );

      wrs_log("Inserting reservation with data: " . print_r($reservation_data, true));

      // Insert the reservation record
      $reservation_insert_result = $wpdb->insert($reservation_table_name, $reservation_data);

      if ($reservation_insert_result === false) {
        wrs_log("ERROR: Failed to insert reservation record. Database error: " . $wpdb->last_error);
        throw new \Exception("Failed to insert reservation record");
      }

      // Get the ID of the inserted reservation record
      $reservation_id = $wpdb->insert_id;

      // If insert_id doesn't work, try a direct query
      if (!$reservation_id || $reservation_id <= 0) {
        $reservation_id = $wpdb->get_var("SELECT LAST_INSERT_ID()");
        wrs_log("Had to use LAST_INSERT_ID() to get reservation ID: " . $reservation_id);
      }

      // Verify the reservation was inserted correctly
      $reservation_check = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$reservation_table_name} WHERE id = %d",
        $reservation_id
      ));

      if (!$reservation_check) {
        wrs_log("ERROR: Reservation record not found after insertion. Database error: " . $wpdb->last_error);
        throw new \Exception("Reservation record not found after insertion");
      }

      wrs_log("Created new reservation with ID: " . $reservation_id);

      // Create reservation item
      $reservation_item_data = array(
        'quantity' => 1, // Number of nights could be calculated
        'product_id' => $product_id,
        'reservation_id' => $reservation_id,
      );

      wrs_log("Inserting reservation item with data: " . print_r($reservation_item_data, true));

      // Insert the reservation item record
      $item_insert_result = $wpdb->insert($reservation_item_table_name, $reservation_item_data);

      if ($item_insert_result === false) {
        wrs_log("ERROR: Failed to insert reservation item record. Database error: " . $wpdb->last_error);
        throw new \Exception("Failed to insert reservation item record");
      }

      // Get the ID of the inserted reservation item record
      $item_id = $wpdb->insert_id;

      // If insert_id doesn't work, try a direct query
      if (!$item_id || $item_id <= 0) {
        $item_id = $wpdb->get_var("SELECT LAST_INSERT_ID()");
        wrs_log("Had to use LAST_INSERT_ID() to get reservation item ID: " . $item_id);
      }

      // Verify the reservation item was inserted correctly
      $item_check = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$reservation_item_table_name} WHERE id = %d",
        $item_id
      ));

      if (!$item_check) {
        wrs_log("ERROR: Reservation item record not found after insertion. Database error: " . $wpdb->last_error);
        throw new \Exception("Reservation item record not found after insertion");
      }

      wrs_log("Created new reservation item with ID: " . $item_id);

      // Store the mapping between Hostaway reservation ID and our reservation ID
      $this->save_configuration('hostaway_reservation_map_' . $hostaway_reservation['id'], $reservation_id);

      $wpdb->query('COMMIT');

      // Update calendars
      $this->update_product_reservation_calendars();

      return $reservation_id;

    } catch (\Throwable $th) {
      wrs_log("Error creating reservation from Hostaway: " . $th->getMessage());
      if (isset($wpdb) && $wpdb->has_cap('transactions')) {
        $wpdb->query('ROLLBACK');
      }
      return false;
    }
  }

  private function update_reservation_from_hostaway($local_reservation_id, $hostaway_reservation) {
    global $wpdb;
    wrs_log("Updating local reservation #" . $local_reservation_id . " from Hostaway data");

    try {
      // Check if the listingMapId exists in the hostaway_reservation data
      if (!isset($hostaway_reservation['listingMapId'])) {
        wrs_log("ERROR: Hostaway reservation data does not contain listingMapId. Full data: " . print_r($hostaway_reservation, true));
        return false;
      }

      // Log the listingMapId we're looking for
      wrs_log("Looking for product with Hostaway listingMapId: " . $hostaway_reservation['listingMapId']);

      // Find the product with matching listingMapId FIRST before updating anything
      $sql_query = $wpdb->prepare(
        "SELECT id FROM {$wpdb->prefix}" . WODAABE_RESERVATIONS_PRODUCT_TABLE . " WHERE listingMapId = %d",
        $hostaway_reservation['listingMapId']
      );

      // Log the SQL query
      wrs_log("SQL query to find matching product: " . $sql_query);

      $products_with_listing_map_id = $wpdb->get_results($sql_query, ARRAY_A);

      if (empty($products_with_listing_map_id)) {
        wrs_log("ERROR: Cannot find product with listingMapId: " . $hostaway_reservation['listingMapId']);
        wrs_log("Reservation cannot be updated because no matching product was found");
        return false;
      }

      // Use the first product that matches the listingMapId
      $product_id = $products_with_listing_map_id[0]['id'];
      wrs_log("Found product with ID: " . $product_id . " matching listingMapId: " . $hostaway_reservation['listingMapId']);

      // Now that we've confirmed a matching product exists, proceed with updating the reservation
      $wpdb->show_errors();
      $wpdb->query('START TRANSACTION');

      // Get the local reservation
      $local_reservation = $this->get_reservation_full($local_reservation_id);
      if (!$local_reservation) {
        wrs_log("Local reservation #" . $local_reservation_id . " not found");
        $wpdb->query('ROLLBACK');
        return false;
      }

      // Prepare guest name - combine guestName, guestFirstName, and guestLastName
      $first_name = $local_reservation['person']['first_name'];
      $last_name = $local_reservation['person']['last_name'];

      // Combine guestFirstName and guestName for first_name if both are available
      if (!empty($hostaway_reservation['guestFirstName']) && !empty($hostaway_reservation['guestName'])) {
        $first_name = $hostaway_reservation['guestFirstName'] . ' ' . $hostaway_reservation['guestName'];
      }
      // If only guestFirstName is available, use it for first_name
      else if (!empty($hostaway_reservation['guestFirstName'])) {
        $first_name = $hostaway_reservation['guestFirstName'];
      }
      // If only guestName is available, use it for first_name
      else if (!empty($hostaway_reservation['guestName'])) {
        $first_name = $hostaway_reservation['guestName'];
      }

      // If guestLastName is available, use it for last_name
      if (!empty($hostaway_reservation['guestLastName'])) {
        $last_name = $hostaway_reservation['guestLastName'];
      }

      // Check if a better matching person exists using multiple fields
      $person_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE;
      $better_matching_person = null;

      // Only search for a better match if we have new identifying information
      if (!empty($hostaway_reservation['guestEmail']) ||
          !empty($hostaway_reservation['phone']) ||
          (!empty($first_name) && !empty($last_name))) {

        // Build a query to find existing person based on available data
        $query_conditions = array();
        $query_params = array();

        // Check by email if available
        if (!empty($hostaway_reservation['guestEmail'])) {
          $query_conditions[] = "email = %s";
          $query_params[] = $hostaway_reservation['guestEmail'];
        }

        // Check by phone if available
        if (!empty($hostaway_reservation['phone'])) {
          $query_conditions[] = "phone = %s";
          $query_params[] = $hostaway_reservation['phone'];
        }

        // Check by name combination if both first and last name are available
        if (!empty($first_name) && !empty($last_name)) {
          $query_conditions[] = "(first_name = %s AND last_name = %s)";
          $query_params[] = $first_name;
          $query_params[] = $last_name;
        }

        // Only proceed with the query if we have conditions and exclude the current person ID
        if (!empty($query_conditions)) {
          $sql = "SELECT id FROM {$person_table_name} WHERE (" . implode(" OR ", $query_conditions) . ") AND id != %d";
          $query_params[] = $local_reservation['person_id'];
          $prepared_sql = $wpdb->prepare($sql, ...$query_params);
          wrs_log("Looking for better matching person with query: " . $prepared_sql);
          $better_matching_person = $wpdb->get_row($prepared_sql);
        }
      }

      if ($better_matching_person) {
        // If we found a better matching person, update the reservation to use that person
        $new_person_id = $better_matching_person->id;
        wrs_log("Found better matching person with ID: " . $new_person_id . ". Updating reservation to use this person instead of " . $local_reservation['person_id']);

        // Update the reservation to use the new person
        $wpdb->update(
          $wpdb->prefix . WODAABE_RESERVATIONS_TABLE,
          array('person_id' => $new_person_id),
          array('id' => $local_reservation_id)
        );

        // Update our local reference to use the new person ID
        $local_reservation['person_id'] = $new_person_id;
      }

      // Update person information
      $wpdb->update(
        $person_table_name,
        array(
          'first_name' => $first_name,
          'last_name' => $last_name,
          'email' => $hostaway_reservation['guestEmail'] ?? $local_reservation['person']['email'],
          'phone' => $hostaway_reservation['phone'] ?? $local_reservation['person']['phone'],
          'country' => $hostaway_reservation['guestCountry'] ?? $local_reservation['person']['country'],
          'comment' => $hostaway_reservation['guestNote'] ?? $local_reservation['person']['comment'],
        ),
        array('id' => $local_reservation['person_id'])
      );

      // Get the total price from the reservation
      $total_price = !empty($hostaway_reservation['totalPrice']) ? floatval($hostaway_reservation['totalPrice']) : 0;

      // Update payment information if total price is available
      if ($total_price > 0) {
        $payment_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE;
        $wpdb->update(
          $payment_table_name,
          array(
            'amount' => $total_price,
            'currency' => $hostaway_reservation['currency'] ?? 'EUR',
          ),
          array('id' => $local_reservation['payment_id'])
        );
        wrs_log("Updated payment record with amount: " . $total_price);
      }

      // Determine adult and child counts
      $adult_count = $local_reservation['adult_count'];
      $child_count = $local_reservation['child_count'];

      // If adults field is available and not empty
      if (isset($hostaway_reservation['adults']) && $hostaway_reservation['adults'] !== '') {
        $adult_count = intval($hostaway_reservation['adults']);
      }

      // If children field is available and not empty
      if (isset($hostaway_reservation['children']) && $hostaway_reservation['children'] !== '') {
        $child_count = intval($hostaway_reservation['children']);
      }

      // If neither adults nor children are specified but numberOfGuests is available
      if (($adult_count + $child_count) == 0 && !empty($hostaway_reservation['numberOfGuests'])) {
        // Assume all guests are adults if not specified
        $adult_count = intval($hostaway_reservation['numberOfGuests']);
      }

      // Update reservation information
      $reservation_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
      $wpdb->update(
        $reservation_table_name,
        array(
          'date_arrive' => $hostaway_reservation['arrivalDate'],
          'date_departure' => $hostaway_reservation['departureDate'],
          'adult_count' => $adult_count,
          'child_count' => $child_count,
        ),
        array('id' => $local_reservation_id)
      );

      // Store the mapping between Hostaway reservation ID and our reservation ID
      $config_key = 'hostaway_reservation_map_' . $hostaway_reservation['id'];
      $this->save_configuration($config_key, $local_reservation_id);
      wrs_log("Stored mapping between local reservation #" . $local_reservation_id . " and Hostaway reservation #" . $hostaway_reservation['id']);

      $wpdb->query('COMMIT');

      // Update calendars
      $this->update_product_reservation_calendars();

      return true;

    } catch (\Throwable $th) {
      wrs_log("Error updating reservation from Hostaway: " . $th->getMessage());
      if (isset($wpdb) && $wpdb->has_cap('transactions')) {
        $wpdb->query('ROLLBACK');
      }
      return false;
    }
  }

  /**
   * Harmonize existing reservations between Wodaabe and Hostaway
   * Called when API synchronization is first enabled for a product
   */
  public function harmonize_reservations($product_id) {
    wrs_log("Starting harmonization process for product #" . $product_id);

    // Get the product details
    $product = $this->get_by_id(WODAABE_RESERVATIONS_PRODUCT_TABLE, $product_id);

    // Check if the product has a listingMapId
    if (empty($product['listingMapId'])) {
        wrs_log("ERROR: Cannot harmonize reservations - Product does not have a Hostaway listing ID");
        return array('success' => false, 'error' => 'No Hostaway listing ID configured');
    }

    // Get token and validate
    $token = $this->get_hostaway_token();
    if (empty($token)) {
        wrs_log("ERROR: Cannot harmonize reservations - Authentication token is null");
        return array('success' => false, 'error' => 'Authentication failed - no valid token');
    }

    try {
        // Fetch all local reservations for this product
        $local_reservations = $this->get_product_reservations($product_id);
        wrs_log("Found " . count($local_reservations) . " local reservations for product #" . $product_id);

        // Fetch all Hostaway reservations for this listing
        $hostaway_reservations = $this->get_all_hostaway_reservations($product['listingMapId']);
        wrs_log("Found " . count($hostaway_reservations) . " Hostaway reservations for listing #" . $product['listingMapId']);

        $created_count = 0;
        $updated_count = 0;

        // Process each Hostaway reservation
        foreach ($hostaway_reservations as $hostaway_reservation) {
            // Check if this reservation already exists locally
            $local_match = $this->find_matching_local_reservation($hostaway_reservation, $local_reservations);

            if (!$local_match) {
                wrs_log("No local match found for Hostaway reservation #" . $hostaway_reservation['id'] . ". Creating locally...");
                // Create the reservation locally
                $result = $this->create_reservation_from_hostaway($hostaway_reservation);
                if ($result) {
                    $created_count++;
                    wrs_log("Successfully created local reservation from Hostaway reservation #" . $hostaway_reservation['id']);
                }
            } else {
                wrs_log("Found matching local reservation #" . $local_match['id'] . " for Hostaway reservation #" . $hostaway_reservation['id'] . ". Updating...");
                // Update the local reservation with Hostaway data
                $result = $this->update_reservation_from_hostaway($local_match['id'], $hostaway_reservation);
                if ($result) {
                    $updated_count++;
                    wrs_log("Successfully updated local reservation #" . $local_match['id'] . " from Hostaway reservation #" . $hostaway_reservation['id']);
                }

                // Store the mapping for future lookups
                $config_key = 'hostaway_reservation_map_' . $hostaway_reservation['id'];
                $this->save_configuration($config_key, $local_match['id']);
            }
        }

        // Process each local reservation
        $hostaway_created_count = 0;
        foreach ($local_reservations as $local_reservation) {
            // Check if this reservation exists in Hostaway
            $hostaway_match = $this->find_matching_hostaway_reservation($local_reservation, $hostaway_reservations);

            if (!$hostaway_match) {
                wrs_log("No Hostaway match found for local reservation #" . $local_reservation['id'] . ". Creating in Hostaway...");
                // Create the reservation in Hostaway
                $result = $this->create_hostaway_reservation($local_reservation['id']);
                if ($result && $result['success']) {
                    $hostaway_created_count++;
                    wrs_log("Successfully created Hostaway reservation from local reservation #" . $local_reservation['id']);

                    // Store the mapping for future lookups
                    $config_key = 'hostaway_reservation_map_' . $result['hostaway_id'];
                    $this->save_configuration($config_key, $local_reservation['id']);
                }
            }
        }

        // Update calendars
        $this->update_product_reservation_calendars();

        wrs_log("Harmonization completed successfully. Created " . $created_count . " local reservations, updated " . $updated_count . " local reservations, created " . $hostaway_created_count . " Hostaway reservations.");

        return array(
            'success' => true,
            'stats' => array(
                'local_created' => $created_count,
                'local_updated' => $updated_count,
                'hostaway_created' => $hostaway_created_count
            )
        );
    } catch (\Throwable $th) {
        wrs_log("ERROR: Exception during harmonization: " . $th->getMessage());
        wrs_log($th->getTraceAsString());
        return array('success' => false, 'error' => 'Exception during harmonization: ' . $th->getMessage());
    }
  }

  /**
   * Get all reservations for a specific product
   */
  private function get_product_reservations($product_id) {
    global $wpdb;

    $reservation_table = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
    $reservation_item_table = $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE;

    $sql = $wpdb->prepare(
        "SELECT r.*
         FROM {$reservation_table} r
         JOIN {$reservation_item_table} ri ON r.id = ri.reservation_id
         WHERE ri.product_id = %d
         AND r.status != 'Canceled'",
        $product_id
    );

    $reservations = $wpdb->get_results($sql, ARRAY_A);

    // Enhance each reservation with full details
    foreach ($reservations as &$reservation) {
        $reservation = $this->get_reservation_full($reservation['id']);
    }

    return $reservations;
  }

  /**
   * Get all reservations from Hostaway for a specific listing
   */
  private function get_all_hostaway_reservations($listing_map_id) {
    $token = $this->get_hostaway_token();
    if (empty($token)) {
        wrs_log("ERROR: Cannot get Hostaway reservations - Authentication token is null");
        return array();
    }

    try {
        // Get current date
        $current_date = date('Y-m-d');

        // Get reservations from 1 year ago to 1 year in the future
        $start_date = date('Y-m-d', strtotime('-1 year'));
        $end_date = date('Y-m-d', strtotime('+1 year'));

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.hostaway.com/v1/reservations?listingMapId=" . $listing_map_id . "&arrivalStartDate=" . $start_date . "&arrivalEndDate=" . $end_date . "&limit=100",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => array(
                "Authorization: Bearer " . $token,
                "Cache-control: no-cache"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            wrs_log("ERROR: CURL error while getting Hostaway reservations: " . $err);
            return array();
        }

        $data = json_decode($response, true);
        if (!isset($data['result'])) {
            wrs_log("ERROR: Invalid response from Hostaway API");
            wrs_log("Response: " . $response);
            return array();
        }

        // Filter out canceled reservations
        $active_reservations = array();
        foreach ($data['result'] as $reservation) {
            if ($reservation['status'] !== 'cancelled') {
                $active_reservations[] = $reservation;
            }
        }

        wrs_log("Retrieved " . count($active_reservations) . " active reservations from Hostaway");
        return $active_reservations;
    } catch (\Throwable $th) {
        wrs_log("ERROR: Exception while getting Hostaway reservations: " . $th->getMessage());
        return array();
    }
  }

  /**
   * Find a matching local reservation for a Hostaway reservation
   */
  private function find_matching_local_reservation($hostaway_reservation, $local_reservations) {
    // Try to match by Hostaway ID first
    $hostaway_id = $hostaway_reservation['id'];
    $local_id = $this->find_reservation_by_hostaway_id($hostaway_id);

    if ($local_id) {
        foreach ($local_reservations as $local_reservation) {
            if ($local_reservation['id'] == $local_id) {
                wrs_log("Found local reservation #" . $local_id . " matching Hostaway reservation #" . $hostaway_id . " using stored mapping");
                return $local_reservation;
            }
        }
    }

    // Try to match by dates and guest information
    foreach ($local_reservations as $local_reservation) {
        // Match by dates
        if ($local_reservation['date_arrive'] == $hostaway_reservation['arrivalDate'] &&
            $local_reservation['date_departure'] == $hostaway_reservation['departureDate']) {

            wrs_log("Found potential match by dates: Local #" . $local_reservation['id'] . " and Hostaway #" . $hostaway_id);

            // Match by guest email if available
            if (!empty($local_reservation['person']['email']) &&
                !empty($hostaway_reservation['guestEmail']) &&
                $local_reservation['person']['email'] == $hostaway_reservation['guestEmail']) {
                wrs_log("Confirmed match by email");
                return $local_reservation;
            }

            // Match by guest name
            if (!empty($local_reservation['person']['first_name']) &&
                !empty($hostaway_reservation['guestFirstName']) &&
                $local_reservation['person']['first_name'] == $hostaway_reservation['guestFirstName'] &&
                !empty($local_reservation['person']['last_name']) &&
                !empty($hostaway_reservation['guestLastName']) &&
                $local_reservation['person']['last_name'] == $hostaway_reservation['guestLastName']) {
                wrs_log("Confirmed match by name");
                return $local_reservation;
            }

            // If we have a date match and no conflicting guest info, consider it a match
            if ((empty($local_reservation['person']['email']) || empty($hostaway_reservation['guestEmail'])) &&
                (empty($local_reservation['person']['first_name']) || empty($hostaway_reservation['guestFirstName']) ||
                 empty($local_reservation['person']['last_name']) || empty($hostaway_reservation['guestLastName']))) {
                wrs_log("Assuming match based on dates only (no conflicting guest info)");
                return $local_reservation;
            }
        }
    }

    return null;
  }

  /**
   * Find a matching Hostaway reservation for a local reservation
   */
  private function find_matching_hostaway_reservation($local_reservation, $hostaway_reservations) {
    // Try to find by dates and listing ID
    foreach ($hostaway_reservations as $hostaway_reservation) {
        if ($hostaway_reservation['arrivalDate'] == $local_reservation['date_arrive'] &&
            $hostaway_reservation['departureDate'] == $local_reservation['date_departure']) {

            wrs_log("Found potential match by dates: Hostaway #" . $hostaway_reservation['id'] . " and Local #" . $local_reservation['id']);

            // Match by guest email if available
            if (!empty($local_reservation['person']['email']) &&
                !empty($hostaway_reservation['guestEmail']) &&
                $local_reservation['person']['email'] == $hostaway_reservation['guestEmail']) {
                wrs_log("Confirmed match by email");
                return $hostaway_reservation;
            }

            // Match by guest name
            if (!empty($local_reservation['person']['first_name']) &&
                !empty($hostaway_reservation['guestFirstName']) &&
                $local_reservation['person']['first_name'] == $hostaway_reservation['guestFirstName'] &&
                !empty($local_reservation['person']['last_name']) &&
                !empty($hostaway_reservation['guestLastName']) &&
                $local_reservation['person']['last_name'] == $hostaway_reservation['guestLastName']) {
                wrs_log("Confirmed match by name");
                return $hostaway_reservation;
            }

            // If we have a date match and no conflicting guest info, consider it a match
            if ((empty($local_reservation['person']['email']) || empty($hostaway_reservation['guestEmail'])) &&
                (empty($local_reservation['person']['first_name']) || empty($hostaway_reservation['guestFirstName']) ||
                 empty($local_reservation['person']['last_name']) || empty($hostaway_reservation['guestLastName']))) {
                wrs_log("Assuming match based on dates only (no conflicting guest info)");
                return $hostaway_reservation;
            }
        }
    }

    return null;
  }











  private function cancel_reservation($local_reservation_id) {
    global $wpdb;
    wrs_log("Canceling local reservation #" . $local_reservation_id);

    try {
      // Get the local reservation first to check if it exists
      $local_reservation = $this->get_reservation_full($local_reservation_id);
      if (!$local_reservation) {
        wrs_log("ERROR: Local reservation #" . $local_reservation_id . " not found");
        return false;
      }

      // Update reservation status to canceled
      $reservation_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
      $wpdb->update(
        $reservation_table_name,
        array(
          'status' => 'Canceled',
        ),
        array('id' => $local_reservation_id)
      );

      // Update calendars
      $this->update_product_reservation_calendars();

      // Send cancellation email notification if needed
      try {
        $this->sendMail(
          'Réservation annulée',
          'reservation_cancelled',
          $local_reservation_id
        );
        wrs_log("Cancellation notification email sent for reservation #" . $local_reservation_id);
      } catch (\Throwable $e) {
        wrs_log("Failed to send cancellation notification: " . $e->getMessage());
        // Continue with cancellation even if email fails
      }

      return true;

    } catch (\Throwable $th) {
      wrs_log("Error canceling reservation: " . $th->getMessage());
      return false;
    }
  }

  //code mail gun

  public function sendMail(string $subject, string $template, string $resId, string $to = null, string $cc = null)
  {
    //wrs_log("Sending mail " . $subject . " with template " . $template . " and resId " . $resId);
    global $wpdb;

    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_TABLE . " WHERE id = " . $resId;
    //wrs_log($sql);
    $item = $wpdb->get_row($sql, 'ARRAY_A');
    //wrs_log("reservation: " . print_r($item, true));

    // Safety check: Do not send emails for external reservations
    if (isset($item['is_external']) && $item['is_external'] == 1) {
      wrs_log("Skipping email sending for external reservation #" . $resId);
      return;
    }
    $sql2 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE . " WHERE id = " . $item["payment_id"];

    $item2 = $wpdb->get_row($sql2, 'ARRAY_A');

    $sql3 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE . " WHERE id = " . $item["person_id"];

    $item3 = $wpdb->get_row($sql3, 'ARRAY_A');

    $elts = $this->get_reservation_items($item["id"]);

    $location = $this->get_reservation_location($elts[0]["product"]["location_id"]);

    $total_price = ($elts[0]["product"]["price_per_night"] * $elts[0]["quantity"]);
    //wrs_log("Prix total: " . $total_price);

    $reservation = $this->get_email_items($item, $elts, $location, $item2, $item3);
    //wrs_log($reservation);

    if ($to === null) {
        $to = $reservation["Client Email"];
    }

    try {
      //wrs_log("SendMail try start . . .");
      # Instantiate the client.
      $mgClient = Mailgun::create("**************************************************");
      //wrs_log("Mailgun instance created");
      $domain = "mg.wodaabe-stays.com";
      //wrs_log("Domain: " . $domain);
      /*
      $data = array(
            "test" => 10
        );
      */

      $total_guests = ($reservation["Adult count"] + $reservation["Child count"]);
      //wrs_log("Nombre d'invités: " . $total_guests);

      $data = array(
            'Booking_ID' => $reservation["Booking ID"],
            'UUID' => $reservation["UUID"],
            'Date_arrival' => $reservation["Date arrival"],
            'Date_departure' => $reservation["Date departure"],
            'Adult_count' => $reservation["Adult count"],
            'Child_count' => $reservation["Child count"],
            'Product' => $reservation["Product"],
            'Location' => $reservation["Location"],
            'Language' => "English",
            'Currency' => "EURO",
            'Payment_Status' => $reservation["Payment Status"],
            'Payment_ID' => !empty($reservation["Payment ID"]) ? $reservation["Payment ID"] : "-",
            'Client_Name' => $reservation["Client Name"],
            'Client_Email' => $reservation["Client Email"],
            'Client_Phone' => $reservation["Client Phone"],
            'Guest_Name' => isset($reservation["Guest Name"]) ? $reservation["Guest Name"] : "",
            'Guest_Email' => isset($reservation["Guest Email"]) ? $reservation["Guest Email"] : "",
            'Guest_Phone' => isset($reservation["Guest Phone"]) ? $reservation["Guest Phone"] : "",
            'Special_requests' => $reservation["Special requests"],
            'Total_Guests' => $total_guests,
            'Total_Price' => $total_price
        );
        //wrs_log("Mail Data: " . print_r($data, true));
      $params = array(
        'from' => "Wodaabe Stays" . ' <' . "<EMAIL>" . '>',
        'to' => $to,
        'cc' => $cc !== null ? $cc : '',
        'subject' => $subject,
        'template' => $template,
        'h:X-Mailgun-Variables' =>  \json_encode($data),
      );
      //wrs_log("Params: " . print_r($params, true));

      if (!empty($to)) {
          # Make the call to the client.
          $result = $mgClient->messages()->send($domain, $params);
          //wrs_log("Result: " . print_r($result, true));
      }
    } catch (\Throwable $e) {
      throw new Exception('Erreur lors de l\'envoi du mail: ' . $e->getMessage());
    }
  }

  public function handle_email_reminders() {
    //wrs_log("handle_email_reminders start");
    //error_log("handle_email_reminders start");
    //echo "handle_email_reminders start";

	global $wpdb;

	$reservations = $this->get_all_reservations();

    //$this->sendMail("<EMAIL>","TEST","test");
	foreach ($reservations as $reservation) {
		// Skip external reservations - they should not receive email reminders
		if (isset($reservation['is_external']) && $reservation['is_external'] == 1) {
			wrs_log("Skipping email reminder for external reservation #" . $reservation['id']);
			continue;
		}

		$address = $reservation["person"]["email"];
		$headers = array('Content-Type: text/html; charset=UTF-8');
		$subject = '';
		$template = '';
		$message = '';
		switch ($reservation['notif_status']) {
			case null:
			    //wrs_log("Case NULL");
				if ($reservation['status'] === 'Incoming') {
				    //wrs_log("Handling incoming reservation N# " . $reservation['id']);
					// Envoyer un rappel pour une réservation non achevée
					$givenDate = DateTime::createFromFormat('Y-m-d H:i:s', $reservation['date_upd']);
					$now = new DateTimeImmutable();
					$diff = $now->diff($givenDate);

					$isPast30Minutes = $diff->i > 30; // i represents minutes in the DateInterval object

					if ($isPast30Minutes) {
						$subject = 'Rappel : Réservation annulée suite au non paiement';
						$template = 'payement_non_acheve';
						$message = "<!DOCTYPE html>
						<html lang='fr'>
						<head>
						<meta charset='UTF-8'>
						<meta name='viewport' content='width=device-width, initial-scale=1.0'>
						  <title>Réservation non confirmée chez Wodaabe Stays</title>
						  <style>
							/* Include your email CSS styles here */
							body {
							  font-family: Arial, sans-serif;
							  margin: 0;
							  padding: 0;
							  background-color: #f8f9fa;
							}
							.container {
							  max-width: 600px;
							  margin: 0 auto;
							  padding: 20px;
							  background-color: #fff;
							  border-radius: 5px;
							}
							h1 {
							  font-size: 20px;
							  color: #333;
							  margin-bottom: 10px;
							  text-align: center;
							}
							p {
							  font-size: 16px;
							  line-height: 1.5;
							  color: #666;
							  margin-bottom: 10px;
							}
							a {
							  display: inline-block;
							  padding: 10px 20px;
							  background-color: #333;
							  color: #fff;
							  font-size: 16px;
							  border-radius: 5px;
							  cursor: pointer;
							  text-decoration: none;
							}
							.minor-link {
								display: inline;
								background-color: #EEE;
								color: #222;
								padding: 5px;
							}
							.unstyled-link {
								display: inline;
								padding: 0px;
								background-color: #DDD;
								color: #222;
								font-size: 20px;
							}
							footer {
								display: flex;
								align-items: center;
								justify-content: center;
								background-color: #DDD;
								text-align: center;
								min-height: 60px;
								width: 100%;
							}
						  </style>
						</head>
						<body>
						  <div class='container'>
							<h1>Réservation non confirmée</h1>

							<p>Votre souscription à notre offre d’hébergement tout-en-un n’a pas été finalisée, et votre commande n’a malheureusement pas aboutie.

							Nous vous invitons à finaliser votre paiement sur notre plateforme de réservation, et espérons vous (re)voir très bientôt dans un de nos logements.</p>

							<p>Vous pouvez cliquer sur le lien ci-dessous pour poursuivre votre réservation :</p>

							<a href='https://wodaabe-stays.com/book-now/'>Poursuivre ma réservation</a>

							<p>Veuillez vous assurer de suivre la procédure de paiement jusqu'au bout. Si vous rencontrez des difficultés, n'hésitez pas à nous contacter.</p>

							<p>Nous sommes disponibles par mail à l'adresse <a class=\"minor-link\" href=\"mailto:<EMAIL>\"><EMAIL></a> et par téléphone au <a class=\"minor-link\" href=\"tel:+33970707861\">+33 (0)9 70 70 78 61</a>.</p>

							<p>Nous vous remercions de votre compréhension.</p>

							<p>Cordialement,</p>
							<p>L'équipe de Wodaabe Stays</p>
						  </div>
						  <footer>
							<span>Copyright <a class=\"unstyled-link\" href=\"https://wodaabe-stays.com\">wodaabe-stays.com</a> <?php echo date('Y'); ?></span>
						</footer>
						</body>
						</html>";

						// Envoyer l'e-mail si un sujet et un message sont définis
                        if (!empty($subject) && !empty($message)) {
                            //wp_mail($address, $subject, $message, $headers);
                            $this->sendMail($subject, $template, $reservation['id']);
                        }

						wrs_log("Deleting reservation n# " . $reservation['id'] . " ...");
						$wpdb->delete($wpdb->prefix . WODAABE_RESERVATIONS_TABLE, array('id' => $reservation['id']), array('%d'));
						return 1;
					} else {
						break;
					}

				} elseif ($reservation['status'] === 'Active') {
		            //wrs_log("Handling active reservation");
					// Envoyer une notification de réservation validée
					$subject = 'Confirmation de réservation';
					$visitors_count = $reservation['adult_count'] + $reservation['child_count'];
					$total_price = $reservation['items'][0]['quantity'] * $reservation['items'][0]['product']['price_per_night'];
					$template = 'payement_confirmer';
					$message = "<!DOCTYPE html>
					<html lang='fr'>
					<head>
					  <meta charset='UTF-8'>
					  <meta name='viewport' content='width=device-width, initial-scale=1.0'>
					  <title>Réservation confirmée chez Wodaabe Stays</title>
					  <style>
						/* Include your email CSS styles here */
						body {
						  font-family: Arial, sans-serif;
						  margin: 0;
						  padding: 0;
						  background-color: #f8f9fa;
						}
						.container {
						  max-width: 600px;
						  margin: 0 auto;
						  padding: 20px;
						  background-color: #fff;
						  border-radius: 5px;
						}
						h1 {
						  font-size: 24px;
						  color: #333;
						  margin-bottom: 10px;
						  text-align: center;
						}
						p {
						  font-size: 16px;
						  line-height: 1.5;
						  color: #666;
						  margin-bottom: 10px;
						}
						a {
						  display: inline-block;
						  padding: 10px 20px;
						  background-color: #333;
						  color: #fff;
						  font-size: 16px;
						  border-radius: 5px;
						  cursor: pointer;
						  text-decoration: none;
						}
						svg {
						  width: 120px;
						  height: 120px;
						  margin: 20px auto;
						}
						.img-reservation {
						  display: block;
						  margin: 0 auto;
						  max-width: 400px;
						  border-radius: 10px;
						}
						.text-reservation {
						  font-size: 20px;
						  color: #333;
						  text-align: center;
						}
						.unstyled-link {
							display: inline;
							padding: 0px;
							background-color: #DDD;
							color: #222;
							font-size: 20px;
						}
						footer {
							display: flex;
							align-items: center;
							justify-content: center;
							background-color: #DDD;
							text-align: center;
							min-height: 60px;
							width: 100%;
						}
					  </style>
					</head>
					<body>
					  <div class='container'>
						<h1>Réservation confirmée</h1>

						<img class='img-reservation' src='{$reservation['items'][0]['product']['picture']}' alt='{$reservation['items'][0]['product']['title']}'>

						<p class='text-reservation'>
						  Cher {$reservation['person']['first_name']} {$reservation['person']['last_name']},<br />

						  nous avons le plaisir de vous confirmer votre réservation chez Wodaabe Stays.

						  Nous attendons votre séjour avec impatience.

						  Retrouvez ci-dessous les détails de votre réservation :

						  <ul>
							<li>Date d'arrivée : {$reservation['date_arrive']}</li>
							<li>Date de départ : {$reservation['date_departure']}</li>
							<li>Nombre de personnes : {$visitors_count}</li>
							<li>Prix total : {$total_price}</li>
						  </ul>

						  Pour toute question, n'hésitez pas à nous contacter.

						  <br />Bien cordialement,
						  <br />L'équipe de Wodaabe Stays
						</p>
					  </div>
					  <footer>
							<span>Copyright <a class=\"unstyled-link\" href=\"https://wodaabe-stays.com\">wodaabe-stays.com</a> <?php echo date('Y'); ?></span>
						</footer>
					</body>
					</html>";

					$table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
					$wpdb->update(
						$table_name,
						array(
							'notif_status' => 'CONFIRMED',
						),
						array('id' => $reservation['id']),
					);
				}
				break;
			case "CONFIRMED":
			    //wrs_log("Case CONFIRMED");
				// Vérifier si la date d'arrivée est demain
				$current_time = new DateTime(date("Y-m-d H:i:s"));
                $time_arrive = new DateTime($reservation['date_arrive'] . ' ' . '08:00:00');

                $diffInSeconds = abs($time_arrive->getTimestamp() - $current_time->getTimestamp());

                // Check if the difference is less than or equal to 24 hours (86400 seconds)
				if ($diffInSeconds <= 86400) {
					$subject = 'Rappel : Votre séjour commence demain';
					$template = 'debut_sejour';
					$message = "<!DOCTYPE html>
					<html lang='fr'>
					<head>
					  <meta charset='UTF-8'>
					  <meta name='viewport' content='width=device-width, initial-scale=1.0'>
					  <title>Séjour imminent chez Wodaabe Stays</title>
					  <style>
						/* Include your email CSS styles here */
						body {
						  font-family: Arial, sans-serif;
						  margin: 0;
						  padding: 0;
						  background-color: #f8f9fa;
						}
						.container {
						  max-width: 600px;
						  margin: 0 auto;
						  padding: 20px;
						  background-color: #fff;
						  border-radius: 5px;
						}
						h1 {
						  font-size: 24px;
						  color: #333;
						  margin-bottom: 10px;
						}
						p {
						  font-size: 16px;
						  line-height: 1.5;
						  color: #666;
						  margin-bottom: 10px;
						}
						a {
						  display: inline-block;
						  padding: 10px 20px;
						  background-color: #333;
						  color: #fff;
						  font-size: 16px;
						  border-radius: 5px;
						  cursor: pointer;
						  text-decoration: none;
						}
						svg {
						  width: 120px;
						  height: 120px;
						  margin: 20px auto;
						}
						.img-reservation {
						  display: block;
						  margin: 0 auto;
						  max-width: 400px;
						  border-radius: 10px;
						}
						.text-reservation {
						  font-size: 20px;
						  color: #333;
						}
						.text-welcome {
						  font-size: 24px;
						  color: #333;
						  margin-bottom: 10px;
						}
						.unstyled-link {
							display: inline;
							padding: 0px;
							background-color: #DDD;
							color: #222;
							font-size: 20px;
						}
						footer {
							display: flex;
							align-items: center;
							justify-content: center;
							background-color: #DDD;
							text-align: center;
							min-height: 60px;
							width: 100%;
						}
					  </style>
					</head>
					<body>
					  <div class='container'>
						<h1>Début de votre séjour chez Wodaabe Stays</h1>

						<img class='img-reservation' src='{$reservation['items'][0]['product']['picture']}' alt='{$reservation['items'][0]['product']['title']}'>

						<p class='text-welcome'>
						  Cher {$reservation['person']['first_name']} {$reservation['person']['last_name']},
						  c'est un plaisir de vous accueillir dans la communauté Wodaabe Stays ! Nous sommes la veille du début de votre séjour.
						  nous avons le plaisir de vous informer que votre séjour chez Wodaabe Stays débutera demain, {$reservation['date_arrive']}.

						  Nous vous attendons avec impatience et sommes à votre disposition pour toute information supplementaire.

						  Profitez de l’expérience Wodaabe Stays !

						  Bien cordialement,
						  L'équipe de Wodaabe Stays
						</p>
					  </div>
					  <footer>
							<span>Copyright <a class=\"unstyled-link\" href=\"https://wodaabe-stays.com\">wodaabe-stays.com</a> <?php echo date('Y'); ?></span>
						</footer>
					</body>
					</html>";
					$table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
					$wpdb->update(
						$table_name,
						array(
							'notif_status' => 'START',
						),
						array('id' => $reservation['id']),
					);
				}
			case "START":
			    //wrs_log("Case START");
				// Vérifier si la date de d'arrivée est dans un jour
				$current_time = new DateTime(date("Y-m-d H:i:s"));
                $time_departure = new DateTime($reservation["date_departure"] . ' ' . '08:00:00');
                $diffInSeconds = abs($time_departure->getTimestamp() - $current_time->getTimestamp());

                // Check if the difference is less than or equal to 24 hours (86400 seconds)
				if ($diffInSeconds <= 86400) {
					$subject = 'Rappel : Départ imminent';
					$template = 'fin_de_sejour';
					$message = "<!DOCTYPE html>
					<html lang='fr'>
					<head>
					  <meta charset='UTF-8'>
					  <meta name='viewport' content='width=device-width, initial-scale=1.0'>
					  <title>Séjour bientôt terminé chez Wodaabe Stays</title>
					  <style>
						/* Include your email CSS styles here */
						body {
						  font-family: Arial, sans-serif;
						  margin: 0;
						  padding: 0;
						  background-color: #f8f9fa;
						}
						.container {
						  max-width: 600px;
						  margin: 0 auto;
						  padding: 20px;
						  background-color: #fff;
						  border-radius: 5px;
						  text-align: center;
						}
						h1 {
						  font-size: 24px;
						  color: #333;
						  margin-bottom: 10px;
						}
						p {
						  font-size: 16px;
						  line-height: 1.5;
						  color: #666;
						  margin-bottom: 10px;
						}
						a {
						  display: inline-block;
						  padding: 10px 20px;
						  background-color: #333;
						  color: #fff;
						  font-size: 16px;
						  border-radius: 5px;
						  cursor: pointer;
						  text-decoration: none;
						}
						svg {
						  width: 120px;
						  height: 120px;
						  margin: 20px auto;
						}
						.img-reservation {
						  display: block;
						  margin: 0 auto;
						  max-width: 400px;
						  border-radius: 10px;
						}
						.text-reminder {
						  font-size: 24px;
						  color: #333;
						  margin-bottom: 10px;
						  text-align: left;
						}
						.unstyled-link {
							display: inline;
							padding: 0px;
							background-color: #DDD;
							color: #222;
							font-size: 20px;
						}
						footer {
							display: flex;
							align-items: center;
							justify-content: center;
							background-color: #DDD;
							text-align: center;
							min-height: 60px;
							width: 100%;
						}
					  </style>
					</head>
					<body>
					  <div class='container'>
						<h1>Séjour bientôt terminé</h1>

						<img class='img-reservation' src='{$reservation['items'][0]['product']['picture']}' alt='{$reservation['items'][0]['product']['title']}'>

						<p class='text-reminder'>
						  Cher {$reservation['person']['first_name']} {$reservation['person']['last_name']},

						  nous vous rappelons que votre séjour chez Wodaabe Stays se terminera demain, {$reservation['date_departure']}.

						  Nous espérons que vous passez un agréable séjour.

						  Nous vous remercions de votre présence au sein de la communauté Wodaabe Stays, et vous souhaitons un agréable retour.

						  <br />Bien cordialement,
						  <br />L'équipe de Wodaabe Stays
						</p>
					  </div>
					  <footer>
							<span>Copyright <a class=\"unstyled-link\" href=\"https://wodaabe-stays.com\">wodaabe-stays.com</a> <?php echo date('Y'); ?></span>
						</footer>
					</body>
					</html>";
					$table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
					$wpdb->update(
						$table_name,
						array(
							'notif_status' => 'END',
						),
						array('id' => $reservation['id']),
					);
				}
				break;
			case "END":
			    //wrs_log("Case END");
				// Vérifier si on est le lendemain du départ
				$current_time = new DateTime(date("Y-m-d H:i:s"));
                $time_departure = new DateTime($reservation["date_departure"] . ' ' . '08:00:00');

                $diffInSeconds = abs($time_departure->getTimestamp() - $current_time->getTimestamp());

				// Compare the current date and time with the specific time
				if ($diffInSeconds >= 86400) {
					// Current date and time is 24 hours or more after the departure time
					$subject = 'Feedback sur votre séjour chez nous';
					$template = 'demande_de_feedback';
					$message = "<!DOCTYPE html>
					<html lang='fr'>
					<head>
					  <meta charset='UTF-8'>
					  <meta name='viewport' content='width=device-width, initial-scale=1.0'>
					  <title>Feedback sur votre séjour chez Wodaabe Stays</title>
					  <style>
						/* Include your email CSS styles here */
						body {
						  font-family: Arial, sans-serif;
						  margin: 0;
						  padding: 0;
						  background-color: #f8f9fa;
						}
						.container {
						  max-width: 600px;
						  margin: 0 auto;
						  padding: 20px;
						  background-color: #fff;
						  border-radius: 5px;
						  text-align: center;
						}
						h1 {
						  font-size: 24px;
						  color: #333;
						  margin-bottom: 10px;
						}
						p {
						  font-size: 16px;
						  line-height: 1.5;
						  color: #666;
						  margin-bottom: 10px;
						}
						a {
						  display: inline-block;
						  padding: 10px 20px;
						  background-color: #333;
						  color: #fff;
						  font-size: 16px;
						  border-radius: 5px;
						  cursor: pointer;
						  text-decoration: none;
						}
						svg {
						  width: 120px;
						  height: 120px;
						  margin: 20px auto;
						}
						.img-reservation {
						  display: block;
						  margin: 0 auto;
						  max-width: 400px;
						  border-radius: 10px;
						}
						.text-feedback {
						  font-size: 24px;
						  color: #333;
						  margin-bottom: 10px;
						  text-align: left;
						}
						.unstyled-link {
							display: inline;
							padding: 0px;
							background-color: #DDD;
							color: #222;
							font-size: 20px;
						}
						footer {
							display: flex;
							align-items: center;
							justify-content: center;
							background-color: #DDD;
							text-align: center;
							min-height: 60px;
							width: 100%;
						}
					  </style>
					</head>
					<body>
					  <div class='container'>
						<h1>Feedback sur votre séjour</h1>

						<img class='img-reservation' src='{$reservation['items'][0]['product']['picture']}' alt='{$reservation['items'][0]['product']['title']}'>

						<p class='text-feedback'>
						  Cher {$reservation['person']['first_name']} {$reservation['person']['last_name']},

						  nous espérons que vous avez passé un agréable séjour chez Wodaabe Stays.

						  Nous serions ravis de recevoir vos impressions sur votre expérience.

						  Nous vous prions en quelques instants de nous faire part de vos commentaires en cliquant sur le lien ci-dessous.

						  Votre avis nous est précieux et nous permettra d'améliorer nos services.

						  Merci d'avance pour votre collaboration.
						  <br />
						  Bien cordialement,
						  <br />L'équipe de Wodaabe Stays
						</p>

						<a href='https://maps.app.goo.gl/TZxKEdsUrfdPr1HP8' target='_blank'>
						  Donner mon avis
						</a>
					  </div>
					  <footer>
							<span>Copyright <a class=\"unstyled-link\" href=\"https://wodaabe-stays.com\">wodaabe-stays.com</a> <?php echo date('Y'); ?></span>
						</footer>
					</body>
					</html>";

					$table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
					$wpdb->update(
						$table_name,
						array(
							'notif_status' => 'ARCHIVED',
						),
						array('id' => $reservation['id']),
					);
				}
				break;
			default:
			    //wrs_log("Case ARCHIVED");
				break;
		}

        // Envoyer l'e-mail si un sujet et un message sont définis
        if (!empty($subject) && !empty($message)) {
            //wp_mail($address, $subject, $message, $headers);
            $this->sendMail($subject, $template, $reservation['id']);
        }
	}
  }

  public function handle_stripe_webhook(WP_REST_Request $request) {
	// Verify the Stripe signature
	$payload = $request->get_body();
	// $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];
	$sig_header = $request->get_header('Stripe-Signature');
	$endpoint_secret = $_ENV['STRIPE_WEBHOOK_SECRET'];
	$event = null;

	try {
		$event = \Stripe\Webhook::constructEvent(
			$payload, $sig_header, $endpoint_secret
		);
		wrs_log("Stripe webhook event : " . json_decode($event, true));
	} catch(\UnexpectedValueException $e) {
        // Invalid payload
      wp_send_json(['Error parsing payload' => $e->getMessage()], 400);
      exit();
    } catch(\Stripe\Exception\SignatureVerificationException $e) {
        // Invalid signature
        wp_send_json(['Error verifying webhook signature: ' => $e->getMessage()], 400);
        exit();
    }
	catch (\Exception $e) {
		// Invalid signature
		wrs_log("Webhook error: " . print_r($e));
		return new WP_Error('invalid_signature', 'Invalid Stripe Signature', array('status' => 403));
	}

    // Handle the event
	switch ($event->type) {
		case 'checkout.session.completed':
			$this->update_stripe_payment_status($event->data->object->client_reference_id, $event->data->object->payment_intent);
			break;
	}

	return new WP_REST_Response('Webhook Received', 200);
  }

  public function create_checkout_session() {
	wrs_log("create_checkout_session() started");
    global $wpdb;
	// Get the current site URL dynamically
	$YOUR_DOMAIN = site_url();
	header('Content-Type: application/json');

	try {
		wrs_log("Stripe try_catch started");
		wrs_log("Payload: ", $_POST);
		wrs_log("Get product with id: " . $_POST['product_id']);

		$product = $this->get_by_id(WODAABE_RESERVATIONS_PRODUCT_TABLE, $_POST['product_id']);
		wrs_log("Product id: " . $_POST['product_id']);
		wrs_log("Product: " . print_r($product, true));
		$total_amount = intval($_POST['total'] * 100); // Total amount in cents
        $original_amount = floatval($_POST['total']); // Store the original amount

		$line_items = [[
			'quantity' => 1, //$_POST['quantity'],
			'price_data' => [
					'currency' => 'EUR',
					'unit_amount' => $total_amount,
					'product_data' => [
						'name' => $product['title'],
						'description' => $product['description'],
						'images' => [$product['picture']],
					],
				],
			]];
		wrs_log("Line items : " . print_r($line_items, true));

		$stripe = new \Stripe\StripeClient($_ENV['STRIPE_API_SECRET_KEY']);

		// Get the current site URL dynamically
		$site_url = site_url();

		$checkout_session = $stripe->checkout->sessions->create([
			'currency' => 'eur',
			'mode' => 'payment',
			'ui_mode' => 'hosted',
			'success_url' => $site_url . '/paiement-reussi/',
			'cancel_url' => $site_url . '/book-now/',
			'line_items' => $line_items,
			'client_reference_id' => $_POST['id'],
		]);

        // Update the payment record with the amount
        if (isset($_POST['id']) && !empty($_POST['id'])) {
            $reservation = $this->get_reservation(intval($_POST['id']));
            if ($reservation && isset($reservation->payment_id)) {
                $payment_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE;
                $wpdb->update(
                    $payment_table_name,
                    array(
                        'amount' => $original_amount,
                    ),
                    array('id' => $reservation->payment_id)
                );
                wrs_log("Updated payment record with amount: " . $original_amount);
            }
        }

		wrs_log('checkout session: ' . $checkout_session);
    	wp_send_json_success($this->success($checkout_session));

	} catch (\Stripe\Exception\ApiErrorException $e) {
		wrs_log('Stripe API Error: ' . $e->getMessage());
		wrs_log('Stripe Error: ' . $e->getError());
		wrs_log('Stripe API Error Code: ' . $e->getStripeCode());
	} catch (\Exception $e) {
		// Handle other types of exceptions (not specific to Stripe) here
		wrs_log('Error: ' . $e->getMessage());
	}
  }

  public function get_email_items($item, $elts, $location, $item2, $item3) {
    if (boolval($item3["is_guess"])) {
        return array(
          'Booking ID' => $item["id"],
          'UUID' => $item["uuid"],
          'Date arrival' => $item["date_arrive"],
          'Date departure' => $item["date_departure"],
          'Adult count' => $item["adult_count"],
          'Child count' => $item["child_count"],
          'Product' => $elts[0]["product"]["title"],
          'Location' => $location["title"],
          'Language' => "English",
          'Currency' => "EURO",
          'Payment Status' => $item2["status"],
          'Payment ID' => !empty($item2["transaction_id"]) ? $item2["transaction_id"] : "-",
          'Client Name' => $item3["buyer_first_name"] . ' ' . $item3["buyer_last_name"],
          'Client Email' => $item3["buyer_email"],
          'Client Phone' => $item3["buyer_phone"],
          'Guest Name' => $item3["first_name"] . ' ' . $item3["last_name"],
          'Guest Email' => $item3["email"],
          'Guest Phone' => $item3["phone"],
          'Special requests' => $item3["comment"]
        );
    } else {
        return array(
          'Booking ID' => $item["id"],
          'UUID' => $item["uuid"],
          'Date arrival' => $item["date_arrive"],
          'Date departure' => $item["date_departure"],
          'Adult count' => $item["adult_count"],
          'Child count' => $item["child_count"],
          'Product' => $elts[0]["product"]["title"],
          'Location' => $location["title"],
          'Language' => "English",
          'Currency' => "EURO",
          'Payment Status' => $item2["status"],
          'Payment ID' => !empty($item2["transaction_id"]) ? $item2["transaction_id"] : "-",
          'Client Name' => $item3["first_name"] . ' ' . $item3["last_name"],
          'Client Email' => $item3["email"],
          'Client Phone' => $item3["phone"],
          'Special requests' => $item3["comment"]
        );
    }
  }
  public function send_mails($item) {
    global $wpdb;

    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_TABLE . " WHERE id = " . $item->id;

    $item = $wpdb->get_row($sql, 'ARRAY_A');

    $sql2 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE . " WHERE id = " . $item["payment_id"];

    $item2 = $wpdb->get_row($sql2, 'ARRAY_A');

    $sql3 = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE . " WHERE id = " . $item["person_id"];

    $item3 = $wpdb->get_row($sql3, 'ARRAY_A');

    $elts = $this->get_reservation_items($item["id"]);
    $location = $this->get_reservation_location($elts[0]["product"]["location_id"]);

    $items = $this->get_email_items($item, $elts, $location, $item2, $item3);

    //$this->send_mail($items);
    sleep(30);
    $this->send_admin_mail($items);
  }

  public function save_order2($id) {
    global $wpdb;

    $person_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE;
    $payment_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE;
    $reservation_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
    $reservation_item_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE;

    $reservation = $this->get_reservation_full($id);

    try {
      $wpdb->show_errors();
      $wpdb->query('START TRANSACTION');
      $wpdb->update($person_table_name,
        array(
          'first_name' => $_POST['first_name'],
          'last_name' => $_POST['last_name'],
          'email' => $_POST['email'],
          'phone' => $_POST['phone'],
          'country' => $_POST['country'],
          'comment' => $_POST['notes'],
          'is_guess' => ($_POST['is_guess'] === 'true') ? 1 : 0,
          'buyer_first_name' => $_POST['buyer_first_name'],
          'buyer_last_name' => $_POST['buyer_last_name'],
          'buyer_email' => $_POST['buyer_email'],
          'buyer_phone' => $_POST['buyer_phone'],
          'agree_policy' => $_POST['agree_policy'],
          'receive_marketing_update' => $_POST['receive_marketing_update'],
        ),
        array('id' => $$reservation["person_id"])
      );

      $wpdb->update($reservation_table_name,
        array(
          'date_arrive' => $_POST['date_arrive'],
          'date_departure' => $_POST['date_departure'],
          'adult_count' => $_POST['adult_count'],
          'child_count' => $_POST['child_count'],
        ),
        array('id' => $$reservation["id"])
      );

      $wpdb->update($reservation_item_table_name,
        array(
          'quantity' => intval($_POST['quantity']),
          'product_id' => intval($_POST['product_id']),
          'reservation_id' => intval($id),
        ),
        array('id' => $$reservation["items"][0]["id"])
      );
      $this->update_product_reservation_calendars();
      $r = $this->get_reservation_full($id);
      return wp_send_json_success($this->success(
        array(
          'reservationId' => $r["id"],
          "reservationUuid" => $r["uuid"],
        )
      ));
      return wp_send_json_success($this->success($this->get_reservation_full($id)));
    } catch (\Throwable $th) {
      wrs_log($th->__toString(), 'ERROR');
      $wpdb->query('ROLLBACK');
    }
    return wp_send_json_error($this->error());
  }
  public function save_order() {
    global $wpdb;
    wrs_log("Save order Post: " . json_encode($_POST));
    $id = $_POST['id'];
    if (!empty($id) && intval($_POST['id']) > 0) {
      return $this->save_order2(intval($id));
    }

    try {
      $wpdb->show_errors();

      // Get the product details to check if it has a Hostaway listing ID
      $product_id = intval($_POST['product_id']);
      $product = $this->get_by_id(WODAABE_RESERVATIONS_PRODUCT_TABLE, $product_id);

      // If the product has a Hostaway listing ID, check availability in Hostaway first
      if (!empty($product['listingMapId'])) {
        wrs_log("Product has Hostaway listing ID: " . $product['listingMapId'] . ". Checking availability in Hostaway...");

        $arrival_date = $_POST['date_arrive'];
        $departure_date = $_POST['date_departure'];

        // Check availability in Hostaway
        $availability_result = $this->check_hostaway_availability($product['listingMapId'], $arrival_date, $departure_date);

        if (!$availability_result['available']) {
          wrs_log("Dates not available in Hostaway: " . ($availability_result['error'] ?? 'Unknown error'));
          return wp_send_json_error($this->error("Selected dates are not available in Hostaway: " . ($availability_result['error'] ?? 'Please try different dates')));
        }

        wrs_log("Dates are available in Hostaway. Proceeding with reservation creation.");
      }

      $wpdb->query('START TRANSACTION');

      $person_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE;
      $payment_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE;
      $reservation_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
      $reservation_item_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE;

      $wpdb->insert($person_table_name,
        array(
          'first_name' => $_POST['first_name'],
          'last_name' => $_POST['last_name'],
          'email' => $_POST['email'],
          'phone' => $_POST['phone'],
          'country' => $_POST['country'],
          'comment' => $_POST['notes'],
          'is_guess' => ($_POST['is_guess'] === 'true') ? 1 : 0,
          'buyer_first_name' => $_POST['buyer_first_name'],
          'buyer_last_name' => $_POST['buyer_last_name'],
          'buyer_email' => $_POST['buyer_email'],
          'buyer_phone' => $_POST['buyer_phone'],
          'agree_policy' => $_POST['agree_policy'],
          'receive_marketing_update' => $_POST['receive_marketing_update'],
        )
      );
      // Get the actual inserted ID using a direct query
      $personId = $wpdb->get_var("SELECT LAST_INSERT_ID()");

      $wpdb->insert($payment_table_name,
        array(
          'transaction_id' => '',
          'status' => 'PENDING',
        )
      );
      // Get the actual inserted ID using a direct query
      $payId = $wpdb->get_var("SELECT LAST_INSERT_ID()");

      $reservationUuid = wrs_uuid();
      $wpdb->insert($reservation_table_name,
        array(
          'date_arrive' => $_POST['date_arrive'],
          'date_departure' => $_POST['date_departure'],
          'uuid' => $reservationUuid,
          'adult_count' => $_POST['adult_count'],
          'child_count' => $_POST['child_count'],
          'status' => 'Incoming',
          'person_id' => $personId,
          'language_id' => $_POST['language_id'],
          'currency_id' => $_POST['currency_id'],
          'payment_id' => $payId,
        )
      );
      // Get the actual inserted ID using a direct query
      $reservationId = $wpdb->get_var("SELECT LAST_INSERT_ID()");

      $wpdb->insert($reservation_item_table_name,
        array(
          'quantity' => intval($_POST['quantity']),
          'product_id' => intval($_POST['product_id']),
          'reservation_id' => intval($reservationId),
        )
      );
      // Get the actual inserted ID using a direct query
      $item_id = $wpdb->get_var("SELECT LAST_INSERT_ID()");
      wrs_log("Created new reservation item with ID: " . $item_id);

      wrs_log($wpdb->last_error, 'INFO');
      $wpdb->query('COMMIT');

      $this->update_product_reservation_calendars();

      // Create the reservation in Hostaway
      if (!empty($product['listingMapId'])) {
        $hostaway_result = $this->create_hostaway_reservation($reservationId);
        if ($hostaway_result && $hostaway_result['success']) {
          wrs_log("Successfully created reservation in Hostaway with ID: " . $hostaway_result['hostaway_id']);
        } else {
          wrs_log("Failed to create reservation in Hostaway: " . ($hostaway_result['error'] ?? 'Unknown error'));
        }
      }

      return wp_send_json_success($this->success(
        array(
          'reservationId' => $reservationId,
          "reservationUuid" => $reservationUuid,
        )
      ));
    } catch (\Throwable $th) {
      wrs_log($th->__toString(), 'ERROR');
      $wpdb->query('ROLLBACK');
    }
    return wp_send_json_error($this->error());
  }

  public function get_locations() {
    global $wpdb;

    $sql = "SELECT * FROM " . $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE . " ORDER BY id DESC ";

    $result = $wpdb->get_results($sql, 'ARRAY_A');

    return wp_send_json_success($this->success($result));
  }

    public function get_products() {
    wrs_log("Getting available products . . .");
    global $wpdb;
    $id = intval($_POST['location_id']);
    wrs_log("Location ID: " . $id);

    // Get all products
    if ($id > 0) {
        $sql = $wpdb->prepare("SELECT * FROM {$wpdb->prefix}" . WODAABE_RESERVATIONS_PRODUCT_TABLE . " WHERE location_id = %d ORDER BY id DESC", $id);
    } else {
        $sql = "SELECT * FROM {$wpdb->prefix}" . WODAABE_RESERVATIONS_PRODUCT_TABLE . " ORDER BY id DESC";
    }
    wrs_log("Product query: " . $sql);
    $result = $wpdb->get_results($sql, 'ARRAY_A');
    wrs_log("Retrieved product details: ");
    wrs_log(print_r($result, true));

    $filtered = [];
    if (isset($_POST["date_arrive"]) && isset($_POST["date_departure"])) {
        $date_arrive = date('Y-m-d', strtotime($_POST["date_arrive"]));
        $date_departure = date('Y-m-d', strtotime($_POST["date_departure"]));

        wrs_log("date_arrive :");
        wrs_log($date_arrive);

        wrs_log("date_departure :");
        wrs_log($date_departure);

        foreach ($result as $p) {
            wrs_log("product details:");
            wrs_log(print_r($p, true));

            // Check for overlapping reservations
            $sql = $wpdb->prepare(
                "SELECT p.id, p.date_arrive, p.date_departure, p.status, l.product_id
                FROM {$wpdb->prefix}" . WODAABE_RESERVATIONS_TABLE . " p
                JOIN {$wpdb->prefix}" . WODAABE_RESERVATIONS_ITEM_TABLE . " l ON p.id = l.reservation_id
                WHERE p.status = 'Active'
                AND l.product_id = %d
                AND (
                    (p.date_arrive >= %s AND p.date_arrive <= %s)
                    OR (p.date_departure >= %s AND p.date_departure <= %s)
                    OR (p.date_arrive < %s AND p.date_departure > %s)
                )",
                $p["id"], $date_arrive, $date_departure, $date_arrive, $date_departure, $date_arrive, $date_departure
            );

            wrs_log("RESERVATIONS query: " . $sql);
            $overlapping_reservations = $wpdb->get_results($sql, ARRAY_A);
            wrs_log("Overlapping reservations: ");
            wrs_log(print_r($overlapping_reservations, true));

            // Check for blocked periods
            $block_sql = $wpdb->prepare(
                "SELECT id, start_date, end_date, comment
                FROM {$wpdb->prefix}" . WODAABE_RESERVATIONS_PRODUCTS_BLOCK_TABLE . "
                WHERE product_id = %d
                AND (
                    (start_date >= %s AND start_date <= %s)
                    OR (end_date >= %s AND end_date <= %s)
                    OR (start_date < %s AND end_date > %s)
                )",
                $p["id"], $date_arrive, $date_departure, $date_arrive, $date_departure, $date_arrive, $date_departure
            );

            wrs_log("BLOCKED PERIODS query: " . $block_sql);
            $blocked_periods = $wpdb->get_results($block_sql, ARRAY_A);
            wrs_log("Blocked periods: ");
            wrs_log(print_r($blocked_periods, true));

            // Check Hostaway availability if the product has a listingMapId
            $hostaway_available = true;
            if (!empty($p['listingMapId'])) {
                wrs_log("Product has Hostaway listing ID: " . $p['listingMapId'] . ". Checking availability in Hostaway...");

                // Check availability in Hostaway
                $availability_result = $this->check_hostaway_availability($p['listingMapId'], $date_arrive, $date_departure);

                if (!$availability_result['available']) {
                    wrs_log("Dates not available in Hostaway: " . ($availability_result['error'] ?? 'Unknown error'));
                    $hostaway_available = false;
                } else {
                    wrs_log("Dates are available in Hostaway.");
                }
            }

            // Add to filtered array only if:
            // 1. No overlapping reservations
            // 2. No blocked periods
            // 3. Available in Hostaway (if product has a listingMapId)
            if (empty($overlapping_reservations) && empty($blocked_periods) && $hostaway_available) {
                wrs_log("Period available...");
                $filtered[] = $p;
            } else {
                wrs_log("Period unavailable...");
                if (!empty($blocked_periods)) {
                    wrs_log("Property is blocked for this period");
                }
                if (!empty($overlapping_reservations)) {
                    wrs_log("Property has overlapping reservations");
                }
                if (!$hostaway_available) {
                    wrs_log("Property is not available in Hostaway for this period");
                }
            }
        }
    }

    return wp_send_json_success($this->success($filtered));
}
  public function register_admin_menus() {
    if (is_admin()) {
      add_menu_page(
        'Manage Wodaabe Stay Reservations',
        'Reservations',
        'manage_options',
        'wodaabe-reservations',
        '',
        'dashicons-admin-multisite',
        13
      );
      add_submenu_page(
        'wodaabe-reservations',
        'Locations',
        'Locations',
        'manage_options',
        'wodaabe-reservations',
        array(new WodaabeReservationsLocations_Table(), 'list_table_page'),
      );
      add_submenu_page(
        'wodaabe-reservations',
        'Products',
        'Products',
        'manage_options',
        'wodaabe-reservations-products',
        array(new WodaabeReservationsProducts_Table(), 'list_table_page'),
      );
      add_submenu_page(
        'wodaabe-reservations',
        'Product Blocking',
        'Product Blocking',
        'manage_options',
        'wodaabe-reservations-product-blocking',
        array($this, 'product_block_page')
      );
      add_submenu_page(
        'wodaabe-reservations',
        'Reservations',
        'Reservations',
        'manage_options',
        'wodaabe-reservations-reservations',
        array(new WodaabeReservations_Table(), 'list_table_page'),
      );
      add_submenu_page(
		'wodaabe-reservations',
		'Reservation Calendar',
		'Reservation Calendar',
		'manage_options',
		'wodaabe-reservations-calendar',
		array(new WodaabeReservationsCalendar_Table(), 'list_table_page'),
	  );
	  add_submenu_page(
		'wodaabe-reservations',
		'Promos codes',
		'Promos codes',
		'manage_options',
		'prices-and-promos',
		array(new Prices_And_Promos_Page(), 'list_table_page')
	  );
    }
  }

  public function product_block_page() {
        $blocking_table = new WodaabeReservationsProductBlocking();


        $action = $_GET['action'] ?? '';
        $new_link = $_SERVER['REQUEST_URI'] . "&action=add";

        wrs_log("Action : " . $action);
        wrs_log("Request URI: " . $_SERVER['REQUEST_URI']);

        if ($action === 'add') {
            $blocking_table->add_page();
        } elseif ($action === 'edit') {
            $blocking_table->edit_page();
        } elseif ($action === 'delete') {
            // Trigger the deletion process
            $blocking_table->process_bulk_action();

            // Optionally, you can redirect after deletion to avoid re-executing the delete action on page reload
            wp_redirect(admin_url('admin.php?page=wodaabe-reservations-product-blocking'));
            exit;
        } else {
            $blocking_table->prepare_items();

            ?>
            <div class="wrap">
                <h1 style="display: inline-block;">Product Blocking</h1>
                <a style="display: inline-block;" href="<?php echo $new_link ?>" class="page-title-action">Add New Blocking</a>
                <p>Block various products for specific time periods</p>
                <?php $blocking_table->display(); ?>
            </div>
            <?php
        }
    }

  /**
   * Creates plugin's tables
   */
  public static function createTables() {

    ob_start();
    global $wpdb;
    $p = new Wodaabe_Reservations();
    wrs_log("Creating Tables ... ", 'INFO');
    try {
      $currency_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_CURRENCY_TABLE;
      $language_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_LANGUAGE_TABLE;
      $product_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCT_TABLE;
      $location_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_LOCATION_TABLE;
      $configuration_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_CONFIGURATION_TABLE;
      $promotional_code_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PROMOTIONAL_CODE_TABLE;
      $person_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PERSON_TABLE;
      $reservation_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_TABLE;
      $reservation_item_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_ITEM_TABLE;
      $payment_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PAYMENT_TABLE;
      $promotional_code_products_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PROMOTIONAL_CODE_PRODUCTS_TABLE;
      $product_block_table_name = $wpdb->prefix . WODAABE_RESERVATIONS_PRODUCTS_BLOCK_TABLE;

      $charset_collate = $wpdb->get_charset_collate();

      $sql = array();

      $sql[] = "CREATE TABLE IF NOT EXISTS $currency_table_name (
				`id` INT(11) NOT NULL AUTO_INCREMENT,
				`name` VARCHAR(254) NOT NULL,
				`code` VARCHAR(254) NOT NULL,
				`date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_{$currency_table_name} PRIMARY KEY(`id`)
			) $charset_collate; ";

      $sql[] = "CREATE TABLE IF NOT EXISTS $language_table_name (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `name` VARCHAR(254) NOT NULL,
        `code` VARCHAR(254) NOT NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_{$language_table_name} PRIMARY KEY(`id`)
      ) $charset_collate; ";

      $sql[] = "CREATE TABLE IF NOT EXISTS $configuration_table_name (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `name` VARCHAR(254) NOT NULL,
        `value` LONGTEXT NOT NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_{$configuration_table_name} PRIMARY KEY(`id`)
      ) $charset_collate; ";

      $sql[] = "CREATE TABLE IF NOT EXISTS $promotional_code_table_name (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `code` VARCHAR(254) NOT NULL,
        `value` DECIMAL(10,2) NOT NULL,
        `description` TEXT NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_{$promotional_code_table_name} PRIMARY KEY(`id`)
      ) $charset_collate; ";

        $sql[] = "CREATE TABLE IF NOT EXISTS $promotional_code_products_table_name (
			`id` INT(11) NOT NULL AUTO_INCREMENT,
			`promo_code_id` INT(11) NOT NULL,
			`product_id` INT(11) NOT NULL,
			`date_add` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT pk_{$promotional_code_products_table_name} PRIMARY KEY(`id`),
			CONSTRAINT fk_{$promotional_code_products_table_name}_promo_code
				FOREIGN KEY (`promo_code_id`)
				REFERENCES $promotional_code_table_name(`id`),
			CONSTRAINT fk_{$promotional_code_products_table_name}_product
				FOREIGN KEY (`product_id`)
				REFERENCES $product_table_name(`id`),
			UNIQUE KEY `unique_product_discount` (`promo_code_id`, `product_id`)
		) $charset_collate;";

		$sql[] = "CREATE TABLE IF NOT EXISTS $product_block_table_name (
          `id` INT(11) NOT NULL AUTO_INCREMENT,
          `product_id` INT(11) NOT NULL,
          `start_date` DATE NOT NULL,
          `end_date` DATE NOT NULL,
          `comment` TEXT NOT NULL,
          `date_add` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          `date_upd` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          CONSTRAINT `fk_{$product_block_table_name}_product_id` FOREIGN KEY (`product_id`) REFERENCES {$product_table_name} (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) $charset_collate; ";

      $sql[] = "CREATE TABLE IF NOT EXISTS $location_table_name (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `title` VARCHAR(254) NOT NULL,
        `picture` VARCHAR(254) NOT NULL,
        `btn_text` VARCHAR(254) NOT NULL,
        `link` VARCHAR(254) NULL,
        `page` VARCHAR(254) NULL,
        `logo` VARCHAR(254) NULL,
        `position` INT NOT NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_{$location_table_name} PRIMARY KEY(`id`)
      ) $charset_collate; ";

      $sql[] = "CREATE TABLE IF NOT EXISTS $product_table_name (
    		`id` INT(11) NOT NULL AUTO_INCREMENT,
    		`title` VARCHAR(254) NOT NULL,
    		`picture` VARCHAR(254) NOT NULL,
    		`capacity` INT NOT NULL,
    		`description` TEXT NOT NULL,
            `price_per_night` DECIMAL(10,2) NOT NULL,
            `rate_text` LONGTEXT NOT NULL,
    		`date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
            `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
            `location_id` INT NOT NULL,
            `sync_url` VARCHAR(254) NULL,
			`price_listing_id` VARCHAR(255) COLLATE utf8mb4_unicode_ci NULL,
            `listingMapId` INT NULL,
            CONSTRAINT pk_{$product_table_name} PRIMARY KEY(`id`),
            CONSTRAINT fk_{$product_table_name}_location_id FOREIGN KEY(`location_id`) REFERENCES {$location_table_name}(`id`) ON DELETE CASCADE ON UPDATE CASCADE
    	) $charset_collate; ";

      $sql[] = "CREATE TABLE IF NOT EXISTS $person_table_name (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `first_name` VARCHAR(254) NULL,
        `last_name` VARCHAR(254) NULL,
        `email` VARCHAR(254) NULL,
        `phone` VARCHAR(254) NULL,
        `country` VARCHAR(254) NULL,
        `comment` VARCHAR(254) NULL,
        `is_guess` BOOLEAN DEFAULT 1,
        `buyer_first_name` VARCHAR(254) NULL,
        `buyer_last_name` VARCHAR(254) NULL,
        `buyer_email` VARCHAR(254) NULL,
        `buyer_phone` VARCHAR(254) NULL,
        `agree_policy` BOOLEAN DEFAULT 0,
        `receive_marketing_update` BOOLEAN DEFAULT 0,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_{$person_table_name} PRIMARY KEY(`id`)
      ) $charset_collate; ";

      $sql[] = "CREATE TABLE IF NOT EXISTS $payment_table_name (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `transaction_id` VARCHAR(254) NULL,
        `status` VARCHAR(254) NULL,
        `amount` DECIMAL(10,2) NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `expire_at` INT UNSIGNED NOT NULL DEFAULT 0,
        `url` VARCHAR(500) DEFAULT NULL,
        CONSTRAINT pk_{$payment_table_name} PRIMARY KEY(`id`)
      ) $charset_collate; ";

      $sql[] = "CREATE TABLE IF NOT EXISTS $reservation_table_name (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `date_arrive` VARCHAR(254) NULL,
        `date_departure` VARCHAR(254) NULL,
        `uuid` VARCHAR(254) NOT NULL,
        `adult_count` INT NULL,
        `child_count` INT NULL,
        `status` VARCHAR(254) NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `person_id` INT NULL,
        `language_id` INT NULL,
        `currency_id` INT NULL,
        `payment_id` INT NULL,
        `notif_status` VARCHAR(20) NULL,
        `is_external` BOOLEAN DEFAULT 0,
        CONSTRAINT pk_{$reservation_table_name} PRIMARY KEY(`id`),
        CONSTRAINT fk_{$reservation_table_name}_person_id FOREIGN KEY(`person_id`) REFERENCES {$person_table_name}(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT fk_{$reservation_table_name}_language_id FOREIGN KEY(`language_id`) REFERENCES {$language_table_name}(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT fk_{$reservation_table_name}_currency_id FOREIGN KEY(`currency_id`) REFERENCES {$currency_table_name}(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT fk_{$reservation_table_name}_payment_id FOREIGN KEY(`payment_id`) REFERENCES {$payment_table_name}(`id`) ON DELETE SET NULL ON UPDATE CASCADE
      ) $charset_collate; ";

      $sql[] = "CREATE TABLE IF NOT EXISTS $reservation_item_table_name (
        `id` INT(11) NOT NULL AUTO_INCREMENT,
        `quantity` INT NOT NULL,
        `product_id` INT NULL,
        `reservation_id` INT NOT NULL,
        `date_add` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        `date_upd` TIMESTAMP  DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT pk_{$reservation_item_table_name} PRIMARY KEY(`id`),
        CONSTRAINT fk_{$reservation_item_table_name}_product_id FOREIGN KEY(`product_id`) REFERENCES {$product_table_name}(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT fk_{$reservation_item_table_name}_reservation_id FOREIGN KEY(`reservation_id`) REFERENCES {$reservation_table_name}(`id`) ON DELETE CASCADE ON UPDATE CASCADE
      ) $charset_collate; ";

      require_once ABSPATH . 'wp-admin/includes/upgrade.php';
      foreach ($sql as $q) {
        $r = dbDelta($q);
        wrs_log("Query " . json_encode($r) . ' | ' . $q, 'INFO');
      }
    } catch (\Throwable $th) {
      wrs_log("Tables creation error !", 'ERROR');
    }
    wrs_log("Tables creation completed !" . ob_get_clean(), 'INFO');
  }

  public function register_fields() {
    register_setting('general', 'woodabe_contact_email', 'esc_attr');
    add_settings_field('woodabe_contact_email', '<label for="woodabe_contact_email">' . __('Woodabe Contact Email', 'woodabe_contact_email') . '</label>', array(&$this, 'fields_html'), 'general');
  }
  public function fields_html() {
    $value = get_option('woodabe_contact_email', '');
    echo '<input style="min-width: 332px;" type="email" id="woodabe_contact_email" name="woodabe_contact_email" value="' . $value . '" />';
  }

  public function send_otp_mail($data) {
    try {
      $result = wp_mail(
        $data["Your Email"],
        "[Wodaabe Stay] Verify email  #" . $data["Your code"],
        $this->getMailBody1($data),
        $this->getMailHeaders($data),
        []
      );
    } catch (Exception $e) {
      die($e->getMessage());
    }
  }

  public function send_mail($data) {

    $email = trim($data["Client Email"]);
    wrs_log("Client email: " . $email);
    wrs_log("=========");
    $body = $this->getMailBody2($data);
    $headers = $this->getMailHeaders($data);

    //wrs_log($body);
    wrs_log("=========");
    wrs_log($headers);

    try {
      $result = wp_mail(
        $email,
        "[Wodaabe Stay] Your reservation #" . $data["Booking ID"],
        $body,
        $headers,
        []
      );
      wrs_log("========= result 2 === " . strval($result));
    } catch (\Throwable $th) {
      wrs_log($th->__toString(), 'ERROR');
    }
  }

  public function send_admin_mail($data) {
    wrs_log("=========");
    $body = $this->getMailBody3($data);
    //$headers = $this->getMailHeaders($data);
    $headers[] = 'From: Wodaabe Stay <<EMAIL>>';
    $headers[] = 'Cc: Kevin Yahoo <<EMAIL>>';
    //wrs_log($body);
    wrs_log("=========> Headers");
    wrs_log($headers);
    try {
      $value = get_option('woodabe_contact_email', '<EMAIL>');
      $result = wp_mail(
        $value,
        "[Wodaabe Stay] New reservation #" . $data["Booking ID"],
        $body,
        $headers,
        []
      );
      var_dump($result);
      wrs_log("========= result 1 === " . strval($result));
    } catch (\Throwable $th) {
      wrs_log($th->__toString(), 'ERROR');
    }
  }

  public function getMailBody1($items) {

    ob_start();
    ?>

   <!--[if gte mso 15]>
	<xml>
		<o:OfficeDocumentSettings>
		<o:AllowPNG/>
		<o:PixelsPerInch>96</o:PixelsPerInch>
		</o:OfficeDocumentSettings>
	</xml>
	<![endif]-->
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Wodaabe Stay</title>
    <style type="text/css">
		p{
			margin:10px 0;
			padding:0;
		}
		table{
			border-collapse:collapse;
		}
		h1,h2,h3,h4,h5,h6{
			display:block;
			margin:0;
			padding:0;
		}
		img,a img{
			border:0;
			height:auto;
			outline:none;
			text-decoration:none;
		}
		body,#bodyTable,#bodyCell{
			height:100%;
			margin:0;
			padding:0;
			width:100%;
		}
		#outlook a{
			padding:0;
		}
		img{
			-ms-interpolation-mode:bicubic;
		}
		table{
			mso-table-lspace:0pt;
			mso-table-rspace:0pt;
		}
		.ReadMsgBody{
			width:100%;
		}
		.ExternalClass{
			width:100%;
		}
		p,a,li,td,blockquote{
			mso-line-height-rule:exactly;
		}
		a[href^=tel],a[href^=sms]{
			color:inherit;
			cursor:default;
			text-decoration:none;
		}
		p,a,li,td,body,table,blockquote{
			-ms-text-size-adjust:100%;
			-webkit-text-size-adjust:100%;
		}
		.ExternalClass,.ExternalClass p,.ExternalClass td,.ExternalClass div,.ExternalClass span,.ExternalClass font{
			line-height:100%;
		}
		a[x-apple-data-detectors]{
			color:inherit !important;
			text-decoration:none !important;
			font-size:inherit !important;
			font-family:inherit !important;
			font-weight:inherit !important;
			line-height:inherit !important;
		}
		#bodyCell{
			padding:50px 50px;
		}
		.templateContainer{
			max-width:600px !important;
			border:0;
		}
		a.mcnButton{
			display:block;
		}
		.mcnTextContent{
			word-break:break-word;
		}
		.mcnTextContent img{
			height:auto !important;
		}
		.mcnDividerBlock{
			table-layout:fixed !important;
		}
		/***** Make theme edits below if needed *****/
		/* Page - Background Style */
		body,#bodyTable{
			background-color:#e9eaec;
		}
		/* Page - Heading 1 */
		h1{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:26px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 2 */
		h2{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:22px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 3 */
		h3{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:20px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 4 */
		h4{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:18px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Header - Header Style */
		#templateHeader{
			border-top:0;
			border-bottom:0;
			padding-top:0;
			padding-bottom:20px;
			text-align: center;
		}
		/* Body - Body Style */
		#templateBody{
			background-color:#FFFFFF;
			border-top:0;
			border: 1px solid #c1c1c1;
			padding-top:0;
			padding-bottom:0px;
		}
		/* Body -Body Text */
		#templateBody .mcnTextContent,
		#templateBody .mcnTextContent p{
			color:#555555;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:14px;
			line-height:150%;
		}
		/* Body - Body Link */
		#templateBody .mcnTextContent a,
		#templateBody .mcnTextContent p a{
			color:#ff7f50;
			font-weight:normal;
			text-decoration:underline;
		}
		/* Footer - Footer Style */
		#templateFooter{
			background-color:#e9eaec;
			border-top:0;
			border-bottom:0;
			padding-top:12px;
			padding-bottom:12px;
		}
		/* Footer - Footer Text */
		#templateFooter .mcnTextContent,
		#templateFooter .mcnTextContent p{
			color:#cccccc;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:12px;
			line-height:150%;
			text-align:center;
		}
		/* Footer - Footer Link */
		#templateFooter .mcnTextContent a,
		#templateFooter .mcnTextContent p a{
			color:#cccccc;
			font-weight:normal;
			text-decoration:underline;
		}
		@media only screen and (min-width:768px){
			.templateContainer{
				width:600px !important;
			}
		}
		@media only screen and (max-width: 480px){
			body,table,td,p,a,li,blockquote{
				-webkit-text-size-adjust:none !important;
			}
		}
		@media only screen and (max-width: 480px){
			body{
				width:100% !important;
				min-width:100% !important;
			}
		}
		@media only screen and (max-width: 680px){
			#bodyCell{
				padding:20px 20px !important;
			}
		}
		@media only screen and (max-width: 480px){
			.mcnTextContentContainer{
				max-width:100% !important;
				width:100% !important;
			}
		}
	</style>
	<center>
		<table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;background-color: #e9eaec;">
			<tbody><tr>
				<td align="center" valign="top" id="bodyCell" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 50px 50px;width: 100%;">
					<!-- BEGIN TEMPLATE // -->
					<!--[if gte mso 9]>
					<table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
					<tr>
					<td align="center" valign="top" width="600" style="width:600px;">
					<![endif]-->
					<table border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;border: 0;max-width: 600px !important;">
												<tbody><tr>
							<td valign="top" id="templateBody" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #FFFFFF;border-top: 0;border: 1px solid #c1c1c1;padding-top: 0;padding-bottom: 0px;">
								<table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
									<tbody class="mcnTextBlockOuter">
										<tr>
											<td valign="top" class="mcnTextBlockInner" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
												<table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnTextContentContainer">
													<tbody>
														<tr>
															<td valign="top" style="padding-top: 30px;padding-right: 30px;padding-bottom: 30px;padding-left: 30px;" class="mcnTextContent">

                                            <?php
foreach ($items as $i => $val) {?>
                                                  <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>

                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong><?php echo $i ?></strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;"><?php echo $val ?></td>
                                            </tr>
                                            </tbody>
                                            </table>

                                            <?php }
    ?>
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr>
							<td valign="top" id="templateFooter" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #e9eaec;border-top: 0;border-bottom: 0;padding-top: 12px;padding-bottom: 12px;">
								<table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
									<tbody class="mcnTextBlockOuter">
										<tr>
											<td valign="top" class="mcnTextBlockInner" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
												<table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnTextContentContainer">
													<tbody>
														<tr>
															<td valign="top" class="mcnTextContent" style="padding-top: 9px;padding-right: 18px;padding-bottom: 9px;padding-left: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #aaa;font-family: Helvetica;font-size: 12px;line-height: 150%;text-align: center;">

																<!-- Footer content -->
																Copyright <a href="https://wodaabe-stays.com" style="color:#bbbbbb;">wodaabe-stays.com</a> <?php echo date("Y"); ?>
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
					</tbody></table>
					<!--[if gte mso 9]>
					</td>
					</tr>
					</table>
					<![endif]-->
					<!-- // END TEMPLATE -->
					</td>
				</tr>
			</tbody></table>
		</center>
<?php
return ob_get_clean();

  }


  public function getMailBody2($items) {
    unset($items["UUID"]);
    unset($items["Language"]);
    unset($items["Currency"]);
    unset($items["Payment ID"]);
    unset($items["Client Email"]);
    unset($items["Client Phone"]);

    $nom = $items["Client Name"];

    ob_start();
    ?>

   <!--[if gte mso 15]>
	<xml>
		<o:OfficeDocumentSettings>
		<o:AllowPNG/>
		<o:PixelsPerInch>96</o:PixelsPerInch>
		</o:OfficeDocumentSettings>
	</xml>
	<![endif]-->
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Wodaabe Stay</title>
    <style type="text/css">
		p{
			margin:10px 0;
			padding:0;
		}
		table{
			border-collapse:collapse;
		}
		h1,h2,h3,h4,h5,h6{
			display:block;
			margin:0;
			padding:0;
		}
		img,a img{
			border:0;
			height:auto;
			outline:none;
			text-decoration:none;
		}
		body,#bodyTable,#bodyCell{
			height:100%;
			margin:0;
			padding:0;
			width:100%;
		}
		#outlook a{
			padding:0;
		}
		img{
			-ms-interpolation-mode:bicubic;
		}
		table{
			mso-table-lspace:0pt;
			mso-table-rspace:0pt;
		}
		.ReadMsgBody{
			width:100%;
		}
		.ExternalClass{
			width:100%;
		}
		p,a,li,td,blockquote{
			mso-line-height-rule:exactly;
		}
		a[href^=tel],a[href^=sms]{
			color:inherit;
			cursor:default;
			text-decoration:none;
		}
		p,a,li,td,body,table,blockquote{
			-ms-text-size-adjust:100%;
			-webkit-text-size-adjust:100%;
		}
		.ExternalClass,.ExternalClass p,.ExternalClass td,.ExternalClass div,.ExternalClass span,.ExternalClass font{
			line-height:100%;
		}
		a[x-apple-data-detectors]{
			color:inherit !important;
			text-decoration:none !important;
			font-size:inherit !important;
			font-family:inherit !important;
			font-weight:inherit !important;
			line-height:inherit !important;
		}
		#bodyCell{
			padding:50px 50px;
		}
		.templateContainer{
			max-width:600px !important;
			border:0;
		}
		a.mcnButton{
			display:block;
		}
		.mcnTextContent{
			word-break:break-word;
		}
		.mcnTextContent img{
			height:auto !important;
		}
		.mcnDividerBlock{
			table-layout:fixed !important;
		}
		/***** Make theme edits below if needed *****/
		/* Page - Background Style */
		body,#bodyTable{
			background-color:#e9eaec;
		}
		/* Page - Heading 1 */
		h1{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:26px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 2 */
		h2{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:22px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 3 */
		h3{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:20px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 4 */
		h4{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:18px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Header - Header Style */
		#templateHeader{
			border-top:0;
			border-bottom:0;
			padding-top:0;
			padding-bottom:20px;
			text-align: center;
		}
		/* Body - Body Style */
		#templateBody{
			background-color:#FFFFFF;
			border-top:0;
			border: 1px solid #c1c1c1;
			padding-top:0;
			padding-bottom:0px;
		}
		/* Body -Body Text */
		#templateBody .mcnTextContent,
		#templateBody .mcnTextContent p{
			color:#555555;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:14px;
			line-height:150%;
		}
		/* Body - Body Link */
		#templateBody .mcnTextContent a,
		#templateBody .mcnTextContent p a{
			color:#ff7f50;
			font-weight:normal;
			text-decoration:underline;
		}
		/* Footer - Footer Style */
		#templateFooter{
			background-color:#e9eaec;
			border-top:0;
			border-bottom:0;
			padding-top:12px;
			padding-bottom:12px;
		}
		/* Footer - Footer Text */
		#templateFooter .mcnTextContent,
		#templateFooter .mcnTextContent p{
			color:#cccccc;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:12px;
			line-height:150%;
			text-align:center;
		}
		/* Footer - Footer Link */
		#templateFooter .mcnTextContent a,
		#templateFooter .mcnTextContent p a{
			color:#cccccc;
			font-weight:normal;
			text-decoration:underline;
		}
		@media only screen and (min-width:768px){
			.templateContainer{
				width:600px !important;
			}
		}
		@media only screen and (max-width: 480px){
			body,table,td,p,a,li,blockquote{
				-webkit-text-size-adjust:none !important;
			}
		}
		@media only screen and (max-width: 480px){
			body{
				width:100% !important;
				min-width:100% !important;
			}
		}
		@media only screen and (max-width: 680px){
			#bodyCell{
				padding:20px 20px !important;
			}
		}
		@media only screen and (max-width: 480px){
			.mcnTextContentContainer{
				max-width:100% !important;
				width:100% !important;
			}
		}
	</style>
	<center>
		<table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;background-color: #e9eaec;">
			<tbody><tr>
				<td align="center" valign="top" id="bodyCell" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 50px 50px;width: 100%;">
					<!-- BEGIN TEMPLATE // -->
					<!--[if gte mso 9]>
					<table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
					<tr>
					<td align="center" valign="top" width="600" style="width:600px;">
					<![endif]-->
					<table border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;border: 0;max-width: 600px !important;">
												<tbody><tr>
							<td valign="top" id="templateBody" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #FFFFFF;border-top: 0;border: 1px solid #c1c1c1;padding-top: 0;padding-bottom: 0px;">
								<table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
									<tbody class="mcnTextBlockOuter">
										<tr>
											<td valign="top" class="mcnTextBlockInner" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
												<table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnTextContentContainer">
													<tbody>

														<tr>
															<td valign="top" style="padding-top: 30px;padding-right: 30px;padding-bottom: 30px;padding-left: 30px;" class="mcnTextContent">
															 <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
															    <tboby>
															        <tr>
														    <td>

														        Dear <?php echo $nom ?>,  <br><br>
                                                                Thank you for choosing Wodaabe stay for your stay! Would you have your estimated arrival time already?  <br> <br>
                                                                <b style="font-size: 2rem;">Booking details</b>  <br>
														        <br>
														    </td>
														</tr>
															    </tboby>

															</table>

                                            <?php
foreach ($items as $i => $val) {?>
                                                  <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>

                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong><?php echo $i ?></strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;"><?php echo $val ?></td>
                                            </tr>
                                            </tbody>
                                            </table>

                                            <?php }
    ?>

															</td>
														</tr>
														<tr>
														    <td>

														        <br>
														        <br>
														        <center>


														        By e-mail you can reach us from the following address: <EMAIL>  <br>
                                                                Welcome to Wodaabe Stay!  <br> <br>
                                                                Kind regards,  <br>
                                                                Léo / Wodaabe Stay<br/><br/>
                                                                </center>
														    </td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr>
							<td valign="top" id="templateFooter" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #e9eaec;border-top: 0;border-bottom: 0;padding-top: 12px;padding-bottom: 12px;">
								<table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
									<tbody class="mcnTextBlockOuter">
										<tr>
											<td valign="top" class="mcnTextBlockInner" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
												<table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnTextContentContainer">
													<tbody>
														<tr>
															<td valign="top" class="mcnTextContent" style="padding-top: 9px;padding-right: 18px;padding-bottom: 9px;padding-left: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #aaa;font-family: Helvetica;font-size: 12px;line-height: 150%;text-align: center;">

																<!-- Footer content -->
																Copyright <a href="https://wodaabe-stays.com" style="color:#bbbbbb;">wodaabe-stays.com</a> <?php echo date("Y"); ?>
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
					</tbody></table>
					<!--[if gte mso 9]>
					</td>
					</tr>
					</table>
					<![endif]-->
					<!-- // END TEMPLATE -->
					</td>
				</tr>
			</tbody></table>
		</center>
<?php
return ob_get_clean();

  }


  public function getMailBody3($items) {

    ob_start();
    ?>

   <!--[if gte mso 15]>
	<xml>
		<o:OfficeDocumentSettings>
		<o:AllowPNG/>
		<o:PixelsPerInch>96</o:PixelsPerInch>
		</o:OfficeDocumentSettings>
	</xml>
	<![endif]-->
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Wodaabe Stay</title>
    <style type="text/css">
		p{
			margin:10px 0;
			padding:0;
		}
		table{
			border-collapse:collapse;
		}
		h1,h2,h3,h4,h5,h6{
			display:block;
			margin:0;
			padding:0;
		}
		img,a img{
			border:0;
			height:auto;
			outline:none;
			text-decoration:none;
		}
		body,#bodyTable,#bodyCell{
			height:100%;
			margin:0;
			padding:0;
			width:100%;
		}
		#outlook a{
			padding:0;
		}
		img{
			-ms-interpolation-mode:bicubic;
		}
		table{
			mso-table-lspace:0pt;
			mso-table-rspace:0pt;
		}
		.ReadMsgBody{
			width:100%;
		}
		.ExternalClass{
			width:100%;
		}
		p,a,li,td,blockquote{
			mso-line-height-rule:exactly;
		}
		a[href^=tel],a[href^=sms]{
			color:inherit;
			cursor:default;
			text-decoration:none;
		}
		p,a,li,td,body,table,blockquote{
			-ms-text-size-adjust:100%;
			-webkit-text-size-adjust:100%;
		}
		.ExternalClass,.ExternalClass p,.ExternalClass td,.ExternalClass div,.ExternalClass span,.ExternalClass font{
			line-height:100%;
		}
		a[x-apple-data-detectors]{
			color:inherit !important;
			text-decoration:none !important;
			font-size:inherit !important;
			font-family:inherit !important;
			font-weight:inherit !important;
			line-height:inherit !important;
		}
		#bodyCell{
			padding:50px 50px;
		}
		.templateContainer{
			max-width:600px !important;
			border:0;
		}
		a.mcnButton{
			display:block;
		}
		.mcnTextContent{
			word-break:break-word;
		}
		.mcnTextContent img{
			height:auto !important;
		}
		.mcnDividerBlock{
			table-layout:fixed !important;
		}
		/***** Make theme edits below if needed *****/
		/* Page - Background Style */
		body,#bodyTable{
			background-color:#e9eaec;
		}
		/* Page - Heading 1 */
		h1{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:26px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 2 */
		h2{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:22px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 3 */
		h3{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:20px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Page - Heading 4 */
		h4{
			color:#202020;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:18px;
			font-style:normal;
			font-weight:bold;
			line-height:125%;
			letter-spacing:normal;
		}
		/* Header - Header Style */
		#templateHeader{
			border-top:0;
			border-bottom:0;
			padding-top:0;
			padding-bottom:20px;
			text-align: center;
		}
		/* Body - Body Style */
		#templateBody{
			background-color:#FFFFFF;
			border-top:0;
			border: 1px solid #c1c1c1;
			padding-top:0;
			padding-bottom:0px;
		}
		/* Body -Body Text */
		#templateBody .mcnTextContent,
		#templateBody .mcnTextContent p{
			color:#555555;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:14px;
			line-height:150%;
		}
		/* Body - Body Link */
		#templateBody .mcnTextContent a,
		#templateBody .mcnTextContent p a{
			color:#ff7f50;
			font-weight:normal;
			text-decoration:underline;
		}
		/* Footer - Footer Style */
		#templateFooter{
			background-color:#e9eaec;
			border-top:0;
			border-bottom:0;
			padding-top:12px;
			padding-bottom:12px;
		}
		/* Footer - Footer Text */
		#templateFooter .mcnTextContent,
		#templateFooter .mcnTextContent p{
			color:#cccccc;
			font-family: 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif;
			font-size:12px;
			line-height:150%;
			text-align:center;
		}
		/* Footer - Footer Link */
		#templateFooter .mcnTextContent a,
		#templateFooter .mcnTextContent p a{
			color:#cccccc;
			font-weight:normal;
			text-decoration:underline;
		}
		@media only screen and (min-width:768px){
			.templateContainer{
				width:600px !important;
			}
		}
		@media only screen and (max-width: 480px){
			body,table,td,p,a,li,blockquote{
				-webkit-text-size-adjust:none !important;
			}
		}
		@media only screen and (max-width: 480px){
			body{
				width:100% !important;
				min-width:100% !important;
			}
		}
		@media only screen and (max-width: 680px){
			#bodyCell{
				padding:20px 20px !important;
			}
		}
		@media only screen and (max-width: 480px){
			.mcnTextContentContainer{
				max-width:100% !important;
				width:100% !important;
			}
		}
	</style>
	<center>
		<table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;background-color: #e9eaec;">
			<tbody><tr>
				<td align="center" valign="top" id="bodyCell" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 50px 50px;width: 100%;">
					<!-- BEGIN TEMPLATE // -->
					<!--[if gte mso 9]>
					<table align="center" border="0" cellspacing="0" cellpadding="0" width="600" style="width:600px;">
					<tr>
					<td align="center" valign="top" width="600" style="width:600px;">
					<![endif]-->
					<table border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;border: 0;max-width: 600px !important;">
												<tbody><tr>
							<td valign="top" id="templateBody" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #FFFFFF;border-top: 0;border: 1px solid #c1c1c1;padding-top: 0;padding-bottom: 0px;">
								<table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
									<tbody class="mcnTextBlockOuter">
										<tr>
											<td valign="top" class="mcnTextBlockInner" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
												<table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnTextContentContainer">
													<tbody>

														<tr>
															<td valign="top" style="padding-top: 30px;padding-right: 30px;padding-bottom: 30px;padding-left: 30px;" class="mcnTextContent">
															 <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
															    <tboby>
															        <tr>
														   <td>

														        Dear Administrator,  <br><br>
                                                                There's a new reservation available  <br> <br>
                                                                <b style="font-size: 2rem;">Booking details</b>  <br>
														        <br>
														    </td>
														</tr>
															    </tboby>

															</table>


                                            <?php
foreach ($items as $i => $val) {?>
                                                  <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style=" display:block;min-width: 100%;border-collapse: collapse;width:100%;">
                                            <tbody>

                                            <tr>
                                            <td style="color:#333333;padding-top: 20px;padding-bottom: 3px;"><strong><?php echo $i ?></strong></td>
                                            </tr>
                                            <tr>
                                            <td style="color:#555555;padding-top: 3px;padding-bottom: 20px;"><?php echo $val ?></td>
                                            </tr>
                                            </tbody>
                                            </table>

                                            <?php }
    ?>

															</td>
														</tr>

													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
						<tr>
							<td valign="top" id="templateFooter" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #e9eaec;border-top: 0;border-bottom: 0;padding-top: 12px;padding-bottom: 12px;">
								<table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
									<tbody class="mcnTextBlockOuter">
										<tr>
											<td valign="top" class="mcnTextBlockInner" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
												<table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" class="mcnTextContentContainer">
													<tbody>
														<tr>
															<td valign="top" class="mcnTextContent" style="padding-top: 9px;padding-right: 18px;padding-bottom: 9px;padding-left: 18px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #aaa;font-family: Helvetica;font-size: 12px;line-height: 150%;text-align: center;">

																<!-- Footer content -->
																Copyright <a href="https://wodaabe-stays.com" style="color:#bbbbbb;">wodaabe-stays.com</a> <?php echo date("Y"); ?>
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
									</tbody>
								</table>
							</td>
						</tr>
					</tbody></table>
					<!--[if gte mso 9]>
					</td>
					</tr>
					</table>
					<![endif]-->
					<!-- // END TEMPLATE -->
					</td>
				</tr>
			</tbody></table>
		</center>
<?php
return ob_get_clean();

  }

  public function getMailHeaders($data) {
    $e = trim($data["Client Email"]);
    wrs_log("Admin mail: " . $e);
    ob_start();
    ?>
    <<EMAIL>>
    From: Wodaabe Stay <<EMAIL>>
    Reply-To: _EMAIL_
    Content-Type: text/html; charset=utf-8
    <<EMAIL>>
<?php
$body = ob_get_clean();
    $body = str_replace("_EMAIL_", $e, $body);
    wrs_log("Header body: " . $body);
    return $body;
  }
}
?>
